# 新职业实现 - 暗影刺客 & 蜘蛛

## 🆕 新增职业概览

### 速通者阵营 - 暗影刺客
- **职业名称：** 暗影刺客
- **职业定位：** 隐秘杀手，擅长背刺和隐身
- **图标：** 下界合金剑 🗡️
- **颜色：** 深紫色

### 捕猎者阵营 - 蜘蛛
- **职业名称：** 蜘蛛
- **职业定位：** 毒性猎手，拥有跳跃和毒液攻击
- **图标：** 蜘蛛眼 🕷️
- **颜色：** 深红色

## 🗡️ 暗影刺客技能详解

### 被动技能 - 背刺
**技能描述：** 从背后攻击敌人造成额外50%的伤害

**技术实现：**
```java
public void handleShadowAssassinBackstab(Player attacker, Player victim, EntityDamageByEntityEvent event) {
    // 计算受害者的朝向
    Vector victimDirection = victim.getLocation().getDirection().normalize();
    
    // 计算从受害者到攻击者的向量
    Vector toAttacker = attacker.getLocation().toVector().subtract(victim.getLocation().toVector()).normalize();
    
    // 计算受害者朝向与攻击者位置的夹角
    double dotProduct = victimDirection.dot(toAttacker);
    
    // 如果夹角大于120度，说明是背刺
    if (dotProduct < -0.5) { // 约120度以上的角度才算背刺
        // 增加50%伤害
        double originalDamage = event.getDamage();
        double extraDamage = originalDamage * 0.5;
        event.setDamage(originalDamage + extraDamage);
        
        // 播放效果和消息反馈
        attacker.playSound(attacker.getLocation(), Sound.ENTITY_PLAYER_ATTACK_CRIT, 1.0f, 1.2f);
        ComponentUtil.sendMessage(attacker, ComponentUtil.info("背刺成功！造成额外50%伤害"));
    }
}
```

**背刺判定机制：**
- 使用向量点积计算攻击角度
- 只有从受害者背后120度范围内的攻击才算背刺
- 背刺成功时播放暴击音效和特殊消息

### 主动技能 - 魅影无形
**技能描述：** 自己无法被猎人阵营看到，持续30s，直到攻击敌人后会现形，隐身状态下指南针依然可以追踪
**冷却时间：** 60秒

**技术实现：**
```java
private boolean handleShadowAssassinInvisibility(Player player) {
    UUID playerId = player.getUniqueId();
    
    // 设置隐身状态
    long endTime = System.currentTimeMillis() + 30000; // 30秒
    shadowInvisibilityStates.put(playerId, endTime);

    // 对所有猎人阵营玩家隐藏自己
    GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(playerId);
    if (gameSession != null) {
        for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
            if (gameSession.getPlayerRole(onlinePlayer.getUniqueId()) == PlayerRole.HUNTER) {
                onlinePlayer.hidePlayer(plugin, player);
            }
        }
    }
    
    // 30秒后自动现形
    Bukkit.getScheduler().runTaskLater(plugin, () -> {
        if (shadowInvisibilityStates.containsKey(playerId)) {
            revealShadowAssassin(player);
        }
    }, 600L); // 30秒 = 600 tick
    
    return true;
}
```

**隐身机制特点：**
- 只对猎人阵营隐身，速通者仍能看见
- 攻击敌人后立即现形
- 指南针仍可追踪（不影响游戏平衡）
- 30秒后自动现形

## 🕷️ 蜘蛛技能详解

### 被动技能 - 毒液狂飙
**技能描述：** 攻击蜘蛛的人会获得3s中毒效果，每次伤害重置cd

**技术实现：**
```java
public void handleSpiderVenomRage(Player spider, Player attacker) {
    // 给攻击者施加3秒中毒效果
    attacker.addPotionEffect(new PotionEffect(
        PotionEffectType.POISON, 60, 0)); // 3秒 = 60 tick
    
    // 播放效果
    attacker.playSound(attacker.getLocation(), Sound.ENTITY_SPIDER_HURT, 1.0f, 1.0f);
    spider.playSound(spider.getLocation(), Sound.ENTITY_SPIDER_AMBIENT, 1.0f, 1.2f);
    
    // 消息反馈
    ComponentUtil.sendMessage(attacker, ComponentUtil.warning("攻击蜘蛛后中毒了！"));
    ComponentUtil.sendMessage(spider, ComponentUtil.info("毒液狂飙触发！" + attacker.getName() + " 中毒了"));
}
```

**毒液机制：**
- 每次受到攻击都会触发
- 给攻击者施加3秒中毒1效果
- 播放蜘蛛特有的音效

### 主动技能 - 跳跃
**技能描述：** 蜘蛛大跳跃天空，落下时范围内的速通者受到伤害和中毒效果，越在中心伤害越高
**冷却时间：** 24秒

**伤害分布：**
- **最高伤害：** 10伤害 + 中毒5持续6s（中心位置）
- **最低伤害：** 2伤害 + 中毒1持续6s（边缘位置）
- **作用范围：** 8格半径

**技术实现：**
```java
private boolean handleSpiderLeap(Player player) {
    // 向上跳跃
    Vector jumpVelocity = new Vector(0, 2.0, 0);
    player.setVelocity(jumpVelocity);
    
    // 延迟执行落地伤害（约2秒后）
    Bukkit.getScheduler().runTaskLater(plugin, () -> {
        executeSpiderLeapDamage(player);
    }, 40L); // 2秒 = 40 tick
    
    return true;
}

private void executeSpiderLeapDamage(Player spider) {
    Location spiderLoc = spider.getLocation();
    
    // 播放落地效果
    spider.playSound(spiderLoc, Sound.ENTITY_GENERIC_EXPLODE, 1.0f, 1.2f);
    spiderLoc.getWorld().spawnParticle(Particle.EXPLOSION, spiderLoc, 5, 2, 0.5, 2, 0.1);

    // 检查范围内的速通者
    GameSession spiderGameSession = plugin.getGameManager().getGameSessionByPlayer(spider.getUniqueId());
    if (spiderGameSession != null) {
        for (Player target : Bukkit.getOnlinePlayers()) {
            if (spiderGameSession.getPlayerRole(target.getUniqueId()) == PlayerRole.SPEEDRUNNER) {
                double distance = target.getLocation().distance(spiderLoc);
                
                if (distance <= 8.0) { // 8格范围内
                    // 计算伤害和中毒等级（距离越近伤害越高）
                    double damageRatio = Math.max(0.25, (8.0 - distance) / 8.0);
                    
                    // 伤害：2-10
                    double damage = 2.0 + (8.0 * damageRatio);
                    
                    // 中毒等级：1-5，持续时间6秒
                    int poisonLevel = (int) Math.max(0, Math.min(4, damageRatio * 5));
                    
                    // 造成伤害和中毒效果
                    target.damage(damage, spider);
                    target.addPotionEffect(new PotionEffect(PotionEffectType.POISON, 120, poisonLevel));
                }
            }
        }
    }
}
```

**跳跃机制特点：**
- 向上跳跃2格高度
- 2秒后执行落地伤害
- 距离越近伤害和中毒等级越高
- 只对速通者造成伤害

## 🎮 游戏平衡性分析

### 暗影刺客平衡性
**优势：**
- 背刺伤害高，适合偷袭
- 隐身能力强，生存能力好
- 机动性强，适合游击战

**劣势：**
- 正面作战能力一般
- 隐身有时间限制
- 攻击后会暴露位置

**平衡考虑：**
- 背刺需要特定角度，不易触发
- 隐身只对猎人有效，指南针仍可追踪
- 攻击后立即现形，增加风险

### 蜘蛛平衡性
**优势：**
- 被动反击能力强
- 跳跃技能范围伤害高
- 控制能力强（中毒效果）

**劣势：**
- 跳跃技能有预判时间
- 冷却时间较长
- 需要近距离作战

**平衡考虑：**
- 跳跃有2秒延迟，给对手反应时间
- 伤害随距离递减，鼓励精准定位
- 只对速通者造成伤害，避免误伤队友

## 🔧 技术实现亮点

### 1. 角度计算系统
- 使用向量点积精确计算攻击角度
- 120度背刺判定范围合理
- 避免了复杂的三角函数计算

### 2. 隐身系统
- 基于Bukkit的hidePlayer/showPlayer机制
- 区分不同阵营的可见性
- 自动现形和手动现形机制

### 3. 范围伤害系统
- 距离衰减算法
- 动态伤害和效果等级计算
- 粒子效果和音效反馈

### 4. 状态管理
- 时间戳追踪隐身状态
- 自动清理过期状态
- 线程安全的状态操作

## 🧪 测试建议

### 暗影刺客测试
1. **背刺测试：**
   ```
   1. 测试不同角度的攻击
   2. 验证120度背刺判定
   3. 确认50%伤害加成
   ```

2. **隐身测试：**
   ```
   1. 验证只对猎人隐身
   2. 测试攻击后现形
   3. 确认30秒自动现形
   ```

### 蜘蛛测试
1. **毒液狂飙测试：**
   ```
   1. 验证每次攻击都触发中毒
   2. 测试3秒中毒效果
   3. 确认音效和消息反馈
   ```

2. **跳跃测试：**
   ```
   1. 测试跳跃高度和时间
   2. 验证范围伤害计算
   3. 确认距离衰减效果
   ```

## 🎯 总结

成功实现了两个新职业：

- ✅ **暗影刺客：** 隐秘杀手，背刺+隐身组合
- ✅ **蜘蛛：** 毒性猎手，反击+范围攻击组合
- ✅ **技能机制：** 复杂而平衡的技能系统
- ✅ **视觉效果：** 丰富的音效和粒子效果
- ✅ **平衡性：** 每个职业都有优势和劣势

这两个新职业为游戏增加了更多的战术选择和游戏深度！🎮✨
