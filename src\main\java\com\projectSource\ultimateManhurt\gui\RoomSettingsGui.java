package com.projectSource.ultimateManhurt.gui;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.room.Room;
import com.projectSource.ultimateManhurt.room.RoomSettings;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

/**
 * 房间设置GUI
 */
public class RoomSettingsGui extends BaseGui {

    private final Room room;
    private final RoomSettings settings;

    // 操作模式枚举
    public enum OperationMode {
        MOUSE("鼠标操作", "使用鼠标左右键快速调整数值"),
        CHAT("聊天输入", "在聊天栏中输入精确数值");

        private final String displayName;
        private final String description;

        OperationMode(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getDescription() {
            return description;
        }
    }

    // 当前操作模式，默认为鼠标操作
    private OperationMode currentOperationMode = OperationMode.MOUSE;
    
    public RoomSettingsGui(UltimateManhurt plugin, Player player, Room room) {
        super(plugin, player, "<gold><bold>房间设置 - " + (room != null ? room.getName() : "未知房间"), 54);
        this.room = room;
        this.settings = room != null ? room.getSettings() : null;

        // 现在手动调用setupGui()，确保所有字段都已正确初始化
        setupGui();
    }
    
    @Override
    protected void setupGui() {
        // 检查房间是否存在
        if (room == null) {
            setItem(22, createItem(Material.BARRIER, "<red>房间不存在", "<gray>房间可能已被删除"));
            return;
        }

        // 检查是否为房间成员
        if (!room.containsPlayer(player.getUniqueId()) && !room.isOwner(player.getUniqueId())) {
            setItem(22, createItem(Material.BARRIER, "<red>权限不足", "<gray>只有房间成员可以查看设置"));
            return;
        }

        // 填充边框
        fillBorder(Material.GRAY_STAINED_GLASS_PANE);

        // 基础设置
        setupBasicSettings();

        // 游戏规则设置
        setupGameRules();

        // 世界设置
        setupWorldSettings();

        // 胜利条件设置
        setupWinConditions();

        // 控制按钮
        setupControlButtons();
    }
    
    /**
     * 设置基础设置
     */
    private void setupBasicSettings() {
        // 游戏时长
        String gameDurationDesc = currentOperationMode == OperationMode.MOUSE ?
            "<gray>左键: -1分钟 | 右键: +1分钟\n<gray>Shift+左键: -10分钟 | Shift+右键: +10分钟" :
            "<gray>点击后在聊天栏输入具体数值";
        setItem(10, createItem(Material.CLOCK,
            "<yellow>游戏时长",
            "<gray>当前: <white>" + settings.getGameDurationMinutes() + " 分钟",
            "",
            "<yellow>当前模式: <white>" + currentOperationMode.getDisplayName(),
            gameDurationDesc));

        // 世界种子
        String worldSeedDesc = currentOperationMode == OperationMode.MOUSE ?
            "<gray>左键: 打开种子设置GUI\n<gray>右键: 在聊天栏输入种子\n<gray>Shift+左键: 快速随机种子" :
            "<gray>点击后在聊天栏输入种子值";
        String worldSeedExtra = currentOperationMode == OperationMode.MOUSE ?
            "<gray>GUI支持按钮输入、预设种子等多种方式" :
            "<gray>输入0表示随机种子";
        setItem(11, createItem(Material.WHEAT_SEEDS,
            "<yellow>世界种子",
            "<gray>当前: <white>" + (settings.getWorldSeed() == 0 ? "随机" : settings.getWorldSeed()),
            "",
            "<yellow>当前模式: <white>" + currentOperationMode.getDisplayName(),
            worldSeedDesc,
            worldSeedExtra));

        // 游戏难度
        setItem(12, createItem(Material.DIAMOND_SWORD,
            "<yellow>游戏难度",
            "<gray>当前: <white>" + settings.getDifficulty().name(),
            "",
            "<yellow>操作方式:",
            "<gray>点击切换难度",
            "<gray>(和平 → 简单 → 普通 → 困难)"));

        // 最大玩家数
        String maxPlayersDesc = currentOperationMode == OperationMode.MOUSE ?
            "<gray>左键: -1人 | 右键: +1人\n<gray>Shift+左键: -5人 | Shift+右键: +5人" :
            "<gray>点击后在聊天栏输入具体数值";
        setItem(13, createItem(Material.PLAYER_HEAD,
            "<yellow>最大玩家数",
            "<gray>当前: <white>" + settings.getMaxPlayers(),
            "",
            "<yellow>当前模式: <white>" + currentOperationMode.getDisplayName(),
            maxPlayersDesc));

        // 阵营人数设置
        String speedrunnersDesc = currentOperationMode == OperationMode.MOUSE ?
            "<gray>左键: -1人 | 右键: +1人" :
            "<gray>点击后在聊天栏输入具体数值";
        setItem(14, createItem(Material.EMERALD,
            "<green>速通者人数限制",
            "<gray>当前: <white>" + settings.getMaxSpeedrunners() + " 人",
            "",
            "<yellow>当前模式: <white>" + currentOperationMode.getDisplayName(),
            speedrunnersDesc,
            "<gray>建议: 1-3人"));

        String huntersDesc = currentOperationMode == OperationMode.MOUSE ?
            "<gray>左键: -1人 | 右键: +1人" :
            "<gray>点击后在聊天栏输入具体数值";
        setItem(15, createItem(Material.REDSTONE,
            "<red>捕猎者人数限制",
            "<gray>当前: <white>" + settings.getMaxHunters() + " 人",
            "",
            "<yellow>当前模式: <white>" + currentOperationMode.getDisplayName(),
            huntersDesc,
            "<gray>建议: 2-10人"));
    }
    
    /**
     * 设置游戏规则
     */
    private void setupGameRules() {
        // PVP开关
        setItem(19, createToggleItem(Material.IRON_SWORD, "PVP开关", settings.isPvpEnabled()));

        // 友军伤害
        setItem(20, createToggleItem(Material.GOLDEN_SWORD, "友军伤害", settings.isFriendlyFire()));

        // 死亡保留物品
        setItem(21, createToggleItem(Material.CHEST, "死亡保留物品", settings.isKeepInventory()));

        // 自然回血
        setItem(22, createToggleItem(Material.GOLDEN_APPLE, "自然回血", settings.isNaturalRegeneration()));

        // 显示死亡消息
        setItem(23, createToggleItem(Material.PAPER, "显示死亡消息", settings.isShowDeathMessages()));

        // 豁免保护
        setItem(24, createToggleItem(Material.SHIELD, "豁免保护", settings.isImmunityEnabled()));

        // 豁免时长
        String immunityDesc = currentOperationMode == OperationMode.MOUSE ?
            "<gray>左键: -5秒 | 右键: +5秒\n<gray>Shift+左键: -30秒 | Shift+右键: +30秒" :
            "<gray>点击后在聊天栏输入具体数值";
        setItem(25, createItem(Material.CLOCK,
            "<yellow>豁免时长",
            "<gray>当前: <white>" + settings.getImmunityDurationSeconds() + " 秒",
            "<gray>Hunter在此时间内无法伤害Speedrunner",
            "",
            "<yellow>当前模式: <white>" + currentOperationMode.getDisplayName(),
            immunityDesc));
    }
    
    /**
     * 设置世界设置
     */
    private void setupWorldSettings() {
        // 生成结构
        setItem(28, createToggleItem(Material.NETHER_BRICK, "生成结构", settings.isGenerateStructures()));
        
        // 昼夜循环
        setItem(29, createToggleItem(Material.CLOCK, "昼夜循环", settings.isDoDaylightCycle()));
        
        // 天气循环
        setItem(30, createToggleItem(Material.WATER_BUCKET, "天气循环", settings.isDoWeatherCycle()));
        
        // 指南针追踪
        setItem(31, createToggleItem(Material.COMPASS, "指南针追踪", settings.isCompassTracking()));

        // 定位条功能
        setItem(32, createToggleItem(Material.RECOVERY_COMPASS, "定位条功能", settings.isLocatorBar()));

        // 末影珍珠冷却
        setItem(33, createToggleItem(Material.ENDER_PEARL, "末影珍珠冷却", settings.isEnderPearlCooldown()));

        // Ban Pick系统
        setItem(34, createToggleItem(Material.BARRIER, "Ban Pick系统", settings.isBanPickEnabled()));

        // 职业系统
        setItem(35, createToggleItem(Material.ENCHANTED_BOOK, "职业系统", settings.isProfessionSystemEnabled()));

        // 自定义出生点逻辑
        String spawnLogicStatus = settings.isCustomSpawnLogic() ? "<green>已启用" : "<red>已禁用";
        String spawnLogicAction = settings.isCustomSpawnLogic() ? "<gray>点击禁用" : "<gray>点击启用";
        setItem(26, createItem(Material.RESPAWN_ANCHOR, "<yellow>自定义出生点逻辑",
            "<gray>状态: " + spawnLogicStatus,
            "",
            "<gray>启用后将使用改进的出生点算法",
            "<gray>• 避免在洞穴中出生",
            "<gray>• 海洋中自动创建平台",
            "<gray>• 优先选择真正的地表位置",
            "",
            "<yellow>注意: <gray>守卫模式不受此设置影响",
            "",
            spawnLogicAction));
    }
    
    /**
     * 创建开关物品
     */
    private ItemStack createToggleItem(Material material, String name, boolean enabled) {
        String status = enabled ? "<green>已启用" : "<red>已禁用";
        String action = enabled ? "<gray>点击禁用" : "<gray>点击启用";
        
        return createItem(material, "<yellow>" + name, 
            "<gray>状态: " + status,
            action);
    }
    
    /**
     * 设置胜利条件
     */
    private void setupWinConditions() {
        // 胜利模式选择
        com.projectSource.ultimateManhurt.game.VictoryMode currentMode = settings.getVictoryMode();
        setItem(37, createItem(currentMode.getIcon(), "<gold><bold>胜利模式",
            "<gray>当前: <white>" + currentMode.getDisplayName(),
            "<gray>" + currentMode.getDescription(),
            "",
            "<yellow>点击切换胜利模式"));

        // 速通者生命数（所有模式都需要）
        String speedrunnerLivesDesc = currentOperationMode == OperationMode.MOUSE ?
            "<gray>左键: -1生命 | 右键: +1生命" :
            "<gray>点击后在聊天栏输入具体数值";
        setItem(38, createItem(Material.TOTEM_OF_UNDYING, "<green>速通者生命数",
            "<gray>当前: <white>" + (settings.getSpeedrunnerLives() == 0 ? "无限" : settings.getSpeedrunnerLives()),
            "<gray>0表示无限生命",
            "",
            "<yellow>当前模式: <white>" + currentOperationMode.getDisplayName(),
            speedrunnerLivesDesc));

        // 捕猎者生命数（所有模式都需要）
        String hunterLivesDesc = currentOperationMode == OperationMode.MOUSE ?
            "<gray>左键: -1生命 | 右键: +1生命" :
            "<gray>点击后在聊天栏输入具体数值";
        setItem(39, createItem(Material.SKELETON_SKULL, "<red>捕猎者生命数",
            "<gray>当前: <white>" + (settings.getHunterLives() == 0 ? "无限" : settings.getHunterLives()),
            "<gray>0表示无限生命",
            "",
            "<yellow>当前模式: <white>" + currentOperationMode.getDisplayName(),
            hunterLivesDesc));

        // 速通者血量设置
        String speedrunnerHealthDesc = currentOperationMode == OperationMode.MOUSE ?
            "<gray>左键: -2血量 | 右键: +2血量\n<gray>Shift+左键: -10血量 | Shift+右键: +10血量" :
            "<gray>点击后在聊天栏输入具体数值";
        setItem(41, createItem(Material.RED_DYE, "<green>速通者血量",
            "<gray>当前: <white>" + String.format("%.0f", settings.getSpeedrunnerMaxHealth()) + " 血",
            "<gray>范围: 1-100血（默认20血）",
            "",
            "<yellow>当前模式: <white>" + currentOperationMode.getDisplayName(),
            speedrunnerHealthDesc));

        // 捕猎者血量设置
        String hunterHealthDesc = currentOperationMode == OperationMode.MOUSE ?
            "<gray>左键: -2血量 | 右键: +2血量\n<gray>Shift+左键: -10血量 | Shift+右键: +10血量" :
            "<gray>点击后在聊天栏输入具体数值";
        setItem(42, createItem(Material.REDSTONE, "<red>捕猎者血量",
            "<gray>当前: <white>" + String.format("%.0f", settings.getHunterMaxHealth()) + " 血",
            "<gray>范围: 1-100血（默认20血）",
            "",
            "<yellow>当前模式: <white>" + currentOperationMode.getDisplayName(),
            hunterHealthDesc));

        // 目标分数（仅积分模式和混合模式显示）或守卫模式设置
        if (currentMode.requiresScoreSystem()) {
            String targetScoreDesc = currentOperationMode == OperationMode.MOUSE ?
                "<gray>左键: -50分 | 右键: +50分\n<gray>Shift+左键: -100分 | Shift+右键: +100分" :
                "<gray>点击后在聊天栏输入具体数值";
            setItem(43, createItem(Material.GOLD_INGOT, "<yellow>目标分数",
                "<gray>当前: <white>" + settings.getTargetScore() + " 分",
                "<gray>速通者达到此分数即可获胜",
                "",
                "<yellow>当前模式: <white>" + currentOperationMode.getDisplayName(),
                targetScoreDesc));
        } else if (currentMode.requiresGuardSystem()) {
            // 守卫模式设置
            setItem(43, createItem(Material.WITHER_SKELETON_SKULL, "<red><bold>守卫模式设置",
                "<gray>配置守卫模式的各种参数",
                "<gray>凋零血量: <white>" + (int)settings.getWitherMaxHealth(),
                "<gray>攻击间隔: <white>" + settings.getWitherAttackInterval() + "秒",
                "<gray>护盾时长: <white>" + (settings.getWitherShieldDuration() / 60) + "分钟",
                "",
                "<yellow>点击打开详细设置"));
        }

        // 速通者重生
        setItem(44, createToggleItem(Material.RESPAWN_ANCHOR, "速通者重生",
            settings.isAllowSpeedrunnerRespawn()));

        // 捕猎者重生
        setItem(45, createToggleItem(Material.RED_BED, "捕猎者重生",
            settings.isAllowHunterRespawn()));
    }

    /**
     * 设置控制按钮
     */
    private void setupControlButtons() {
        boolean isOwner = room != null && room.isOwner(player.getUniqueId());

        // 玩家管理按钮（房主专用）
        if (isOwner) {
            setItem(46, createItem(Material.PLAYER_HEAD, "<blue><bold>玩家管理",
                "<gray>点击管理房间内的玩家",
                "<yellow>设置玩家角色和权限"));
        } else {
            // 非房主显示玩家列表（只读）
            setItem(46, createItem(Material.PLAYER_HEAD, "<gray><bold>玩家列表",
                "<gray>查看房间内的玩家",
                "<gray>只有房主可以管理玩家"));
        }

        // 邀请管理按钮（仅私人房间的房主可见）
        if (room != null && room.getType() == com.projectSource.ultimateManhurt.room.RoomType.PRIVATE && isOwner) {
            setItem(36, createItem(Material.PAPER, "<yellow><bold>邀请管理",
                "<gray>管理房间邀请",
                "<yellow>邀请玩家加入私人房间"));
        }

        // 里程碑设置按钮（房主专用）
        if (isOwner) {
            com.projectSource.ultimateManhurt.game.scoring.MilestoneSettings milestoneSettings = room.getSettings().getMilestoneSettings();
            String milestoneStatus = milestoneSettings != null ? milestoneSettings.getSettingsSummary() : "默认设置";

            setItem(47, createItem(Material.BOOK, "<gold><bold>里程碑设置",
                "<gray>配置游戏里程碑和分数",
                "<gray>当前: " + milestoneStatus,
                "<yellow>点击管理里程碑设置"));
        }

        // 装备包管理/状态按钮
        String kitStatus = room.getSettings().isStartKitEnabled() ? "<green>已启用" : "<red>已禁用";
        if (isOwner) {
            setItem(48, createItem(Material.CHEST, "<color:#8d5caa><bold>装备包管理",
                "<gray>管理起始装备包设置",
                "<gray>当前状态: " + kitStatus,
                "<yellow>设置速通者和捕猎者的装备"));
        } else {
            setItem(48, createItem(Material.CHEST, "<gray><bold>装备包状态",
                "<gray>状态: " + kitStatus,
                "<gray>只有房主可以修改装备包"));
        }

        // 角色选择按钮（所有房间成员都可用）
        setItem(49, createItem(Material.GOLDEN_SWORD, "<gold><bold>选择阵营",
            "<gray>选择你的阵营进行对战",
            "<yellow>速通者 vs 捕猎者",
            "<gray>ELO将根据表现调整"));

        // 开始游戏按钮（房主专用）
        int gameButtonSlot = 50;
        if (isOwner) {
            if (room != null && room.canStartGame()) {
                setItem(gameButtonSlot, createItem(Material.DIAMOND, "<green><bold>开始游戏",
                    "<gray>点击开始游戏",
                    "<yellow>需要至少1个速通者和1个捕猎者"));
            } else {
                String reason = getCannotStartReason();
                setItem(gameButtonSlot, createItem(Material.BARRIER, "<red>无法开始游戏",
                    "<gray>" + reason));
            }
        } else {
            // 非房主显示游戏状态
            setItem(gameButtonSlot, createItem(Material.CLOCK, "<gray><bold>等待房主开始",
                "<gray>只有房主可以开始游戏"));
        }

        // 操作模式切换按钮（所有玩家可见）
        Material modeIcon = currentOperationMode == OperationMode.MOUSE ? Material.IRON_PICKAXE : Material.WRITABLE_BOOK;
        setItem(53, createItem(modeIcon, "<aqua><bold>操作模式切换",
            "<gray>当前模式: <white>" + currentOperationMode.getDisplayName(),
            "<gray>" + currentOperationMode.getDescription(),
            "",
            "<yellow>点击切换操作模式",
            "<gray>鼠标操作: 左右键快速调整",
            "<gray>聊天输入: 精确数值输入"));

        // 保存设置和重置设置按钮（房主专用）
        if (isOwner) {
            setItem(51, createItem(Material.EMERALD, "<green>保存设置", "<gray>点击保存所有设置"));
            setItem(52, createItem(Material.REDSTONE, "<red>重置设置", "<gray>点击重置为默认设置"));
        }

        setItem(54, createBackButton());
    }
    
    @Override
    public void handleClick(InventoryClickEvent event) {
        event.setCancelled(true);

        if (event.getClickedInventory() != inventory) {
            return;
        }

        // 检查房间是否存在
        if (room == null) {
            sendError("房间不存在或已被删除！");
            playErrorSound();
            close();
            plugin.getGuiManager().openRoomListGui(player);
            return;
        }

        int slot = event.getSlot();
        ItemStack item = event.getCurrentItem();

        if (item == null || item.getType() == Material.AIR) {
            return;
        }

        playClickSound();

        boolean isOwner = room.isOwner(player.getUniqueId());
        boolean isRightClick = event.isRightClick();
        boolean isLeftClick = event.isLeftClick();
        boolean isShiftClick = event.isShiftClick();

        // 非房主只能点击特定按钮
        if (!isOwner) {
            switch (slot) {
                case 46: // 玩家列表（只读）
                    handlePlayerManagement();
                    return;
                case 49: // 选择阵营
                    handleRoleSelection();
                    return;
                case 54: // 返回
                    handleBack();
                    return;
                default:
                    sendError("只有房主可以修改房间设置！");
                    playErrorSound();
                    return;
            }
        }
        
        switch (slot) {
            case 10: // 游戏时长
                handleGameDuration(isLeftClick, isRightClick, isShiftClick);
                break;
            case 11: // 世界种子
                handleWorldSeed(isLeftClick, isRightClick, isShiftClick);
                break;
            case 12: // 游戏难度
                handleDifficulty(isLeftClick, isRightClick, isShiftClick);
                break;
            case 13: // 最大玩家数
                handleMaxPlayers(isLeftClick, isRightClick, isShiftClick);
                break;
            case 14: // 速通者人数限制
                handleMaxSpeedrunners(isLeftClick, isRightClick, isShiftClick);
                break;
            case 15: // 捕猎者人数限制
                handleMaxHunters(isLeftClick, isRightClick, isShiftClick);
                break;
            case 19: // PVP开关
                settings.setPvpEnabled(!settings.isPvpEnabled());
                refresh();
                break;
            case 20: // 友军伤害
                settings.setFriendlyFire(!settings.isFriendlyFire());
                refresh();
                break;
            case 21: // 死亡保留物品
                settings.setKeepInventory(!settings.isKeepInventory());
                refresh();
                break;
            case 22: // 自然回血
                settings.setNaturalRegeneration(!settings.isNaturalRegeneration());
                refresh();
                break;
            case 23: // 显示死亡消息
                settings.setShowDeathMessages(!settings.isShowDeathMessages());
                refresh();
                break;
            case 24: // 豁免保护
                settings.setImmunityEnabled(!settings.isImmunityEnabled());
                refresh();
                break;
            case 25: // 豁免时长
                handleImmunityDuration(isLeftClick, isRightClick, isShiftClick);
                break;
            case 26: // 自定义出生点逻辑
                settings.setCustomSpawnLogic(!settings.isCustomSpawnLogic());
                refresh();
                break;
            case 28: // 生成结构
                settings.setGenerateStructures(!settings.isGenerateStructures());
                refresh();
                break;
            case 29: // 昼夜循环
                settings.setDoDaylightCycle(!settings.isDoDaylightCycle());
                refresh();
                break;
            case 30: // 天气循环
                settings.setDoWeatherCycle(!settings.isDoWeatherCycle());
                refresh();
                break;
            case 31: // 指南针追踪
                settings.setCompassTracking(!settings.isCompassTracking());
                refresh();
                break;
            case 32: // 定位条功能
                settings.setLocatorBar(!settings.isLocatorBar());
                refresh();
                break;
            case 33: // 末影珍珠冷却
                settings.setEnderPearlCooldown(!settings.isEnderPearlCooldown());
                refresh();
                break;
            case 34: // Ban Pick系统
                settings.setBanPickEnabled(!settings.isBanPickEnabled());
                refresh();
                break;
            case 35: // 职业系统
                settings.setProfessionSystemEnabled(!settings.isProfessionSystemEnabled());
                refresh();
                break;
            case 37: // 胜利模式
                handleVictoryMode();
                break;
            case 38: // 速通者生命数
                handleSpeedrunnerLives(isLeftClick, isRightClick, isShiftClick);
                break;
            case 39: // 捕猎者生命数
                handleHunterLives(isLeftClick, isRightClick, isShiftClick);
                break;
            case 40: // 空槽位
                // 不做任何操作
                break;
            case 41: // 速通者血量
                handleSpeedrunnerHealth(isLeftClick, isRightClick, isShiftClick);
                break;
            case 42: // 捕猎者血量
                handleHunterHealth(isLeftClick, isRightClick, isShiftClick);
                break;
            case 43: // 目标分数或守卫模式设置
                if (settings.getVictoryMode().requiresScoreSystem()) {
                    handleTargetScore(isLeftClick, isRightClick, isShiftClick);
                } else if (settings.getVictoryMode().requiresGuardSystem()) {
                    handleGuardModeSettings();
                }
                break;
            case 44: // 速通者重生
                settings.setAllowSpeedrunnerRespawn(!settings.isAllowSpeedrunnerRespawn());
                refresh();
                break;
            case 45: // 捕猎者重生
                settings.setAllowHunterRespawn(!settings.isAllowHunterRespawn());
                refresh();
                break;
            case 36: // 邀请管理
                handleInviteManagement();
                break;
            case 46: // 玩家管理
                handlePlayerManagement();
                break;
            case 47: // 里程碑设置
                handleMilestoneSettings();
                break;
            case 48: // 装备包管理
                handleStartKitManagement();
                break;
            case 49: // 角色选择
                handleRoleSelection();
                break;
            case 50: // 开始游戏
                handleStartGame();
                break;
            case 51: // 保存设置
                handleSave();
                break;
            case 52: // 重置设置
                handleReset();
                break;
            case 53: // 操作模式切换
                handleOperationModeToggle();
                break;
            case 54: // 返回
                handleBack();
                break;
        }
    }
    
    /**
     * 处理游戏时长设置
     */
    private void handleGameDuration(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
        if (currentOperationMode == OperationMode.MOUSE) {
            // 鼠标操作模式
            if (isLeftClick || isRightClick) {
                handleGameDurationMouseOperation(isLeftClick, isRightClick, isShiftClick);
            }
        } else {
            // 聊天输入模式
            handleGameDurationChatInput();
        }
    }

    /**
     * 处理游戏时长的鼠标操作
     */
    private void handleGameDurationMouseOperation(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
        int currentDuration = settings.getGameDurationMinutes();
        int newDuration = currentDuration;

        if (isLeftClick) {
            // 左键：减少
            int decrease = isShiftClick ? 10 : 1;
            newDuration = Math.max(1, currentDuration - decrease);
            if (newDuration != currentDuration) {
                sendSuccess("游戏时长已设置为: " + newDuration + " 分钟");
            } else {
                sendError("游戏时长已达到最小值(1分钟)！");
                playErrorSound();
                return;
            }
        } else if (isRightClick) {
            // 右键：增加
            int increase = isShiftClick ? 10 : 1;
            newDuration = Math.min(180, currentDuration + increase);
            if (newDuration != currentDuration) {
                sendSuccess("游戏时长已设置为: " + newDuration + " 分钟");
            } else {
                sendError("游戏时长已达到最大值(180分钟)！");
                playErrorSound();
                return;
            }
        }

        settings.setGameDurationMinutes(newDuration);
        refresh();
    }

    /**
     * 处理游戏时长的聊天输入
     */
    private void handleGameDurationChatInput() {
        close();

        plugin.getGuiManager().getChatInputManager().startChatInput(
            player,
            "请输入游戏时长（分钟，1-180）：",
            input -> {
                try {
                    int duration = Integer.parseInt(input);
                    if (duration < 1 || duration > 180) {
                        ComponentUtil.sendMessage(player, ComponentUtil.error("游戏时长必须在1-180分钟之间！"));
                    } else {
                        settings.setGameDurationMinutes(duration);
                        ComponentUtil.sendMessage(player, ComponentUtil.success("游戏时长已设置为 " + duration + " 分钟"));
                    }
                } catch (NumberFormatException e) {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("请输入有效的数字！"));
                }

                // 重新打开GUI - 从RoomManager获取最新的房间对象
                Room currentRoom = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
                if (currentRoom != null) {
                    plugin.getGuiManager().openRoomSettingsGui(player, currentRoom);
                } else {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("房间不存在或已被删除"));
                    plugin.getGuiManager().openRoomListGui(player);
                }
            },
            () -> {
                // 取消时重新打开GUI - 从RoomManager获取最新的房间对象
                Room currentRoom = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
                if (currentRoom != null) {
                    plugin.getGuiManager().openRoomSettingsGui(player, currentRoom);
                } else {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("房间不存在或已被删除"));
                    plugin.getGuiManager().openRoomListGui(player);
                }
            }
        );
    }

    /**
     * 处理豁免时长设置
     */
    private void handleImmunityDuration(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
        if (currentOperationMode == OperationMode.MOUSE) {
            // 鼠标操作模式
            if (isLeftClick || isRightClick) {
                handleImmunityDurationMouseOperation(isLeftClick, isRightClick, isShiftClick);
            }
        } else {
            // 聊天输入模式
            handleImmunityDurationChatInput();
        }
    }

    /**
     * 处理豁免时长的鼠标操作
     */
    private void handleImmunityDurationMouseOperation(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
        int currentDuration = settings.getImmunityDurationSeconds();
        int newDuration = currentDuration;

        if (isLeftClick) {
            // 左键：减少
            int decrease = isShiftClick ? 30 : 5;
            newDuration = Math.max(0, currentDuration - decrease);
            if (newDuration != currentDuration) {
                sendSuccess("豁免时长已设置为: " + newDuration + " 秒");
            } else {
                sendError("豁免时长已达到最小值(0秒)！");
                playErrorSound();
                return;
            }
        } else if (isRightClick) {
            // 右键：增加
            int increase = isShiftClick ? 30 : 5;
            newDuration = Math.min(300, currentDuration + increase);
            if (newDuration != currentDuration) {
                sendSuccess("豁免时长已设置为: " + newDuration + " 秒");
            } else {
                sendError("豁免时长已达到最大值(300秒)！");
                playErrorSound();
                return;
            }
        }

        settings.setImmunityDurationSeconds(newDuration);
        refresh();
    }

    /**
     * 处理豁免时长的聊天输入
     */
    private void handleImmunityDurationChatInput() {
        close();

        plugin.getGuiManager().getChatInputManager().startChatInput(
            player,
            "请输入豁免时长（秒，0-300）：",
            input -> {
                try {
                    int duration = Integer.parseInt(input);
                    if (duration < 0 || duration > 300) {
                        ComponentUtil.sendMessage(player, ComponentUtil.error("豁免时长必须在0-300秒之间！"));
                    } else {
                        settings.setImmunityDurationSeconds(duration);
                        ComponentUtil.sendMessage(player, ComponentUtil.success("豁免时长已设置为: " + duration + " 秒"));
                    }
                } catch (NumberFormatException e) {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("请输入有效的数字！"));
                }

                // 重新打开GUI - 从RoomManager获取最新的房间对象
                Room currentRoom = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
                if (currentRoom != null) {
                    plugin.getGuiManager().openRoomSettingsGui(player, currentRoom);
                } else {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("房间不存在或已被删除"));
                    plugin.getGuiManager().openRoomListGui(player);
                }
            },
            () -> {
                // 取消时重新打开GUI - 从RoomManager获取最新的房间对象
                Room currentRoom = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
                if (currentRoom != null) {
                    plugin.getGuiManager().openRoomSettingsGui(player, currentRoom);
                } else {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("房间不存在或已被删除"));
                    plugin.getGuiManager().openRoomListGui(player);
                }
            }
        );
    }

    /**
     * 处理世界种子设置
     */
    private void handleWorldSeed(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
        if (currentOperationMode == OperationMode.MOUSE) {
            // 鼠标操作模式
            if (isShiftClick && isLeftClick) {
                // Shift+左键：快速随机种子
                settings.setWorldSeed(0);
                sendSuccess("世界种子已设置为随机");
                refresh();
            } else if (isLeftClick) {
                // 左键：打开种子设置GUI
                plugin.getGuiManager().openSeedSettingsGui(player, room);
            } else if (isRightClick) {
                // 右键：打开聊天输入来设置具体种子
                handleWorldSeedChatInput();
            }
        } else {
            // 聊天输入模式：直接进入聊天输入状态
            handleWorldSeedChatInput();
        }
    }

    /**
     * 处理世界种子的聊天输入
     */
    private void handleWorldSeedChatInput() {
        close();
        plugin.getGuiManager().getChatInputManager().startChatInput(
            player,
            "请输入世界种子（输入0表示随机）：",
            input -> {
                try {
                    long seed = Long.parseLong(input);
                    settings.setWorldSeed(seed);
                    String seedText = seed == 0 ? "随机" : String.valueOf(seed);
                    ComponentUtil.sendMessage(player, ComponentUtil.success("世界种子已设置为: " + seedText));
                } catch (NumberFormatException e) {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("请输入有效的数字！"));
                }

                // 重新打开GUI
                Room currentRoom = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
                if (currentRoom != null) {
                    plugin.getGuiManager().openRoomSettingsGui(player, currentRoom);
                } else {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("房间不存在或已被删除"));
                    plugin.getGuiManager().openRoomListGui(player);
                }
            },
            () -> {
                // 取消时重新打开GUI
                Room currentRoom = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
                if (currentRoom != null) {
                    plugin.getGuiManager().openRoomSettingsGui(player, currentRoom);
                } else {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("房间不存在或已被删除"));
                    plugin.getGuiManager().openRoomListGui(player);
                }
            }
        );
    }
    
    /**
     * 处理难度设置
     */
    private void handleDifficulty(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
        // 难度设置在两种模式下都是循环切换
        org.bukkit.Difficulty[] difficulties = org.bukkit.Difficulty.values();
        org.bukkit.Difficulty current = settings.getDifficulty();

        // 找到当前难度的索引
        int currentIndex = 0;
        for (int i = 0; i < difficulties.length; i++) {
            if (difficulties[i] == current) {
                currentIndex = i;
                break;
            }
        }

        // 循环切换到下一个难度
        int nextIndex = (currentIndex + 1) % difficulties.length;
        settings.setDifficulty(difficulties[nextIndex]);
        sendSuccess("游戏难度已设置为: " + difficulties[nextIndex].name());
        refresh();
    }
    
    /**
     * 处理最大玩家数设置
     */
    private void handleMaxPlayers(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
        if (currentOperationMode == OperationMode.MOUSE) {
            // 鼠标操作模式
            if (isLeftClick || isRightClick) {
                handleMaxPlayersMouseOperation(isLeftClick, isRightClick, isShiftClick);
            }
        } else {
            // 聊天输入模式
            handleMaxPlayersChatInput();
        }
    }

    /**
     * 处理最大玩家数的鼠标操作
     */
    private void handleMaxPlayersMouseOperation(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
        int currentMaxPlayers = settings.getMaxPlayers();
        int newMaxPlayers = currentMaxPlayers;

        if (isLeftClick) {
            // 左键：减少
            int decrease = isShiftClick ? 5 : 1;
            newMaxPlayers = Math.max(2, currentMaxPlayers - decrease);
            if (newMaxPlayers != currentMaxPlayers) {
                sendSuccess("最大玩家数已设置为: " + newMaxPlayers + " 人");
            } else {
                sendError("最大玩家数已达到最小值(2人)！");
                playErrorSound();
                return;
            }
        } else if (isRightClick) {
            // 右键：增加
            int increase = isShiftClick ? 5 : 1;
            newMaxPlayers = Math.min(50, currentMaxPlayers + increase);
            if (newMaxPlayers != currentMaxPlayers) {
                sendSuccess("最大玩家数已设置为: " + newMaxPlayers + " 人");
            } else {
                sendError("最大玩家数已达到最大值(50人)！");
                playErrorSound();
                return;
            }
        }

        settings.setMaxPlayers(newMaxPlayers);
        refresh();
    }

    /**
     * 处理最大玩家数的聊天输入
     */
    private void handleMaxPlayersChatInput() {
        close();

        plugin.getGuiManager().getChatInputManager().startChatInput(
            player,
            "请输入最大玩家数（2-50）：",
            input -> {
                try {
                    int maxPlayers = Integer.parseInt(input);
                    if (maxPlayers < 2 || maxPlayers > 50) {
                        ComponentUtil.sendMessage(player, ComponentUtil.error("最大玩家数必须在2-50之间！"));
                    } else {
                        settings.setMaxPlayers(maxPlayers);
                        ComponentUtil.sendMessage(player, ComponentUtil.success("最大玩家数已设置为 " + maxPlayers));
                    }
                } catch (NumberFormatException e) {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("请输入有效的数字！"));
                }

                // 重新打开GUI - 从RoomManager获取最新的房间对象
                Room currentRoom = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
                if (currentRoom != null) {
                    plugin.getGuiManager().openRoomSettingsGui(player, currentRoom);
                } else {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("房间不存在或已被删除"));
                    plugin.getGuiManager().openRoomListGui(player);
                }
            },
            () -> {
                // 取消时重新打开GUI - 从RoomManager获取最新的房间对象
                Room currentRoom = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
                if (currentRoom != null) {
                    plugin.getGuiManager().openRoomSettingsGui(player, currentRoom);
                } else {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("房间不存在或已被删除"));
                    plugin.getGuiManager().openRoomListGui(player);
                }
            }
        );
    }
    
    /**
     * 处理保存设置
     */
    private void handleSave() {
        try {
            // 验证设置
            if (!settings.isValid()) {
                sendError("设置无效！请检查配置参数。");
                playErrorSound();
                return;
            }

            // 保存设置（这里可以添加持久化逻辑）
            plugin.getLogger().info("保存房间设置: " + room.getName());

            sendSuccess("设置已保存！");
            playSuccessSound();

            // 通知房间内的其他玩家
            notifyRoomPlayers("房间设置已更新");

        } catch (Exception e) {
            sendError("保存设置失败: " + e.getMessage());
            playErrorSound();
            plugin.getLogger().severe("保存房间设置失败: " + e.getMessage());
        }
    }

    /**
     * 处理重置设置
     */
    private void handleReset() {
        try {
            // 创建新的默认设置
            com.projectSource.ultimateManhurt.room.RoomSettings defaultSettings =
                new com.projectSource.ultimateManhurt.room.RoomSettings(room.getType());

            // 复制默认设置到当前设置
            copySettings(defaultSettings, settings);

            sendSuccess("设置已重置为默认值");
            playSuccessSound();
            refresh();

            // 通知房间内的其他玩家
            notifyRoomPlayers("房间设置已重置为默认值");

        } catch (Exception e) {
            sendError("重置设置失败: " + e.getMessage());
            playErrorSound();
            plugin.getLogger().severe("重置房间设置失败: " + e.getMessage());
        }
    }

    /**
     * 复制设置
     */
    private void copySettings(com.projectSource.ultimateManhurt.room.RoomSettings from,
                             com.projectSource.ultimateManhurt.room.RoomSettings to) {
        to.setGameDurationMinutes(from.getGameDurationMinutes());
        to.setWorldSeed(from.getWorldSeed());
        to.setDifficulty(from.getDifficulty());
        to.setMaxPlayers(from.getMaxPlayers());
        to.setPvpEnabled(from.isPvpEnabled());
        to.setFriendlyFire(from.isFriendlyFire());
        to.setKeepInventory(from.isKeepInventory());
        to.setNaturalRegeneration(from.isNaturalRegeneration());
        to.setShowDeathMessages(from.isShowDeathMessages());
        to.setGenerateStructures(from.isGenerateStructures());
        to.setDoDaylightCycle(from.isDoDaylightCycle());
        to.setDoWeatherCycle(from.isDoWeatherCycle());
        to.setCustomSpawnLogic(from.isCustomSpawnLogic());
        to.setCompassTracking(from.isCompassTracking());
        to.setEnderPearlCooldown(from.isEnderPearlCooldown());
    }

    /**
     * 通知房间内的玩家
     */
    private void notifyRoomPlayers(String message) {
        if (room == null) {
            return; // 房间不存在，无法通知
        }

        for (java.util.UUID playerId : room.getPlayers()) {
            org.bukkit.entity.Player roomPlayer = plugin.getServer().getPlayer(playerId);
            if (roomPlayer != null && roomPlayer.isOnline() && !roomPlayer.equals(player)) {
                com.projectSource.ultimateManhurt.util.ComponentUtil.sendMessage(
                    roomPlayer,
                    com.projectSource.ultimateManhurt.util.ComponentUtil.info(message)
                );
            }
        }
    }
    
    /**
     * 处理操作模式切换
     */
    private void handleOperationModeToggle() {
        // 切换操作模式
        currentOperationMode = (currentOperationMode == OperationMode.MOUSE) ?
            OperationMode.CHAT : OperationMode.MOUSE;

        // 发送切换成功消息
        sendSuccess("操作模式已切换为: " + currentOperationMode.getDisplayName());
        sendInfo(currentOperationMode.getDescription());

        // 刷新GUI以更新按钮显示
        refresh();
    }

    /**
     * 处理返回
     */
    private void handleBack() {
        plugin.getGuiManager().openRoomListGui(player);
    }

    /**
     * 处理邀请管理
     */
    private void handleInviteManagement() {
        if (room == null) {
            sendError("房间不存在！");
            playErrorSound();
            return;
        }

        // 检查是否为私人房间
        if (room.getType() != com.projectSource.ultimateManhurt.room.RoomType.PRIVATE) {
            sendError("只有私人房间可以管理邀请！");
            playErrorSound();
            return;
        }

        // 检查是否为房主
        if (!room.isOwner(player.getUniqueId())) {
            sendError("只有房主可以管理邀请！");
            playErrorSound();
            return;
        }

        close();
        plugin.getGuiManager().openInviteGui(player, room);
    }

    /**
     * 处理里程碑设置
     */
    private void handleMilestoneSettings() {
        if (room == null) {
            sendError("房间不存在！");
            playErrorSound();
            return;
        }

        // 检查是否为房主
        if (!room.isOwner(player.getUniqueId())) {
            sendError("只有房主可以修改里程碑设置！");
            playErrorSound();
            return;
        }

        close();
        plugin.getGuiManager().openMilestoneSettingsGui(player, room);
    }

    /**
     * 处理装备包管理
     */
    private void handleStartKitManagement() {
        if (room == null) {
            sendError("房间不存在！");
            playErrorSound();
            return;
        }

        // 检查是否为房主
        if (!room.isOwner(player.getUniqueId())) {
            sendError("只有房主可以管理装备包！");
            playErrorSound();
            return;
        }

        close();
        plugin.getGuiManager().openStartKitGui(player, room);
    }

    /**
     * 处理玩家管理
     */
    private void handlePlayerManagement() {
        if (room == null) {
            sendError("房间不存在！");
            playErrorSound();
            return;
        }

        close();
        plugin.getGuiManager().openPlayerManagementGui(player, room);
    }

    /**
     * 处理角色选择（天梯模式）
     */
    private void handleRoleSelection() {
        if (room == null) {
            sendError("房间不存在！");
            playErrorSound();
            return;
        }

        // 角色选择现在对所有房间类型都可用

        close();
        plugin.getGuiManager().openRoleSelectionGui(player, room);
    }

    /**
     * 处理速通者生命数设置
     */
    private void handleSpeedrunnerLives(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
        if (currentOperationMode == OperationMode.MOUSE) {
            // 鼠标操作模式
            if (isLeftClick || isRightClick) {
                handleSpeedrunnerLivesMouseOperation(isLeftClick, isRightClick);
            }
        } else {
            // 聊天输入模式
            handleSpeedrunnerLivesChatInput();
        }
    }

    /**
     * 处理速通者生命数的鼠标操作
     */
    private void handleSpeedrunnerLivesMouseOperation(boolean isLeftClick, boolean isRightClick) {
        int currentLives = settings.getSpeedrunnerLives();
        int newLives = currentLives;

        if (isLeftClick) {
            // 左键：减少
            newLives = Math.max(0, currentLives - 1);
            if (newLives != currentLives) {
                String livesText = newLives == 0 ? "无限" : String.valueOf(newLives);
                sendSuccess("速通者生命数已设置为: " + livesText);
            } else {
                sendError("速通者生命数已达到最小值(0=无限)！");
                playErrorSound();
                return;
            }
        } else if (isRightClick) {
            // 右键：增加
            newLives = Math.min(10, currentLives + 1);
            if (newLives != currentLives) {
                String livesText = newLives == 0 ? "无限" : String.valueOf(newLives);
                sendSuccess("速通者生命数已设置为: " + livesText);
            } else {
                sendError("速通者生命数已达到最大值(10)！");
                playErrorSound();
                return;
            }
        }

        settings.setSpeedrunnerLives(newLives);
        refresh();
    }

    /**
     * 处理速通者生命数的聊天输入
     */
    private void handleSpeedrunnerLivesChatInput() {
        close();

        plugin.getGuiManager().getChatInputManager().startChatInput(
            player,
            "请输入速通者生命数（0表示无限，1-10）：",
            input -> {
                try {
                    int lives = Integer.parseInt(input);
                    if (lives < 0 || lives > 10) {
                        ComponentUtil.sendMessage(player, ComponentUtil.error("生命数必须在0-10之间！"));
                    } else {
                        settings.setSpeedrunnerLives(lives);
                        String livesText = lives == 0 ? "无限" : String.valueOf(lives);
                        ComponentUtil.sendMessage(player, ComponentUtil.success("速通者生命数已设置为: " + livesText));
                    }
                } catch (NumberFormatException e) {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("请输入有效的数字！"));
                }

                // 重新打开GUI - 从RoomManager获取最新的房间对象
                Room currentRoom = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
                if (currentRoom != null) {
                    plugin.getGuiManager().openRoomSettingsGui(player, currentRoom);
                } else {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("房间不存在或已被删除"));
                    plugin.getGuiManager().openRoomListGui(player);
                }
            },
            () -> {
                // 取消时重新打开GUI - 从RoomManager获取最新的房间对象
                Room currentRoom = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
                if (currentRoom != null) {
                    plugin.getGuiManager().openRoomSettingsGui(player, currentRoom);
                } else {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("房间不存在或已被删除"));
                    plugin.getGuiManager().openRoomListGui(player);
                }
            }
        );
    }

    /**
     * 处理胜利模式切换
     */
    private void handleVictoryMode() {
        close();

        // 创建胜利模式选择GUI
        plugin.getGuiManager().openVictoryModeSelectionGui(player, room);
    }

    /**
     * 处理守卫模式设置
     */
    private void handleGuardModeSettings() {
        close();

        // 创建守卫模式设置GUI
        plugin.getGuiManager().openGuardModeSettingsGui(player, room);
    }

    /**
     * 处理目标分数设置
     */
    private void handleTargetScore(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
        if (currentOperationMode == OperationMode.MOUSE) {
            // 鼠标操作模式
            if (isLeftClick || isRightClick) {
                handleTargetScoreMouseOperation(isLeftClick, isRightClick, isShiftClick);
            }
        } else {
            // 聊天输入模式
            handleTargetScoreChatInput();
        }
    }

    /**
     * 处理目标分数的鼠标操作
     */
    private void handleTargetScoreMouseOperation(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
        int currentScore = settings.getTargetScore();
        int newScore = currentScore;

        if (isLeftClick) {
            // 左键：减少
            int decrease = isShiftClick ? 100 : 50;
            newScore = Math.max(100, currentScore - decrease);
            if (newScore != currentScore) {
                sendSuccess("目标分数已设置为: " + newScore + " 分");
            } else {
                sendError("目标分数已达到最小值(100分)！");
                playErrorSound();
                return;
            }
        } else if (isRightClick) {
            // 右键：增加
            int increase = isShiftClick ? 100 : 50;
            newScore = Math.min(1000, currentScore + increase);
            if (newScore != currentScore) {
                sendSuccess("目标分数已设置为: " + newScore + " 分");
            } else {
                sendError("目标分数已达到最大值(1000分)！");
                playErrorSound();
                return;
            }
        }

        settings.setTargetScore(newScore);
        refresh();
    }

    /**
     * 处理目标分数的聊天输入
     */
    private void handleTargetScoreChatInput() {
        close();

        plugin.getGuiManager().getChatInputManager().startChatInput(
            player,
            "请输入目标分数（100-1000）：",
            input -> {
                try {
                    int score = Integer.parseInt(input);
                    if (score < 100 || score > 1000) {
                        ComponentUtil.sendMessage(player, ComponentUtil.error("目标分数必须在100-1000之间！"));
                    } else {
                        settings.setTargetScore(score);
                        ComponentUtil.sendMessage(player, ComponentUtil.success("目标分数已设置为: " + score + " 分"));
                    }
                } catch (NumberFormatException e) {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("请输入有效的数字！"));
                }

                // 重新打开GUI - 从RoomManager获取最新的房间对象
                Room currentRoom = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
                if (currentRoom != null) {
                    plugin.getGuiManager().openRoomSettingsGui(player, currentRoom);
                } else {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("房间不存在或已被删除"));
                    plugin.getGuiManager().openRoomListGui(player);
                }
            },
            () -> {
                // 取消时重新打开GUI - 从RoomManager获取最新的房间对象
                Room currentRoom = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
                if (currentRoom != null) {
                    plugin.getGuiManager().openRoomSettingsGui(player, currentRoom);
                } else {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("房间不存在或已被删除"));
                    plugin.getGuiManager().openRoomListGui(player);
                }
            }
        );
    }

    /**
     * 处理捕猎者生命数设置
     */
    private void handleHunterLives(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
        if (currentOperationMode == OperationMode.MOUSE) {
            // 鼠标操作模式
            if (isLeftClick || isRightClick) {
                handleHunterLivesMouseOperation(isLeftClick, isRightClick);
            }
        } else {
            // 聊天输入模式
            handleHunterLivesChatInput();
        }
    }

    /**
     * 处理捕猎者生命数的鼠标操作
     */
    private void handleHunterLivesMouseOperation(boolean isLeftClick, boolean isRightClick) {
        int currentLives = settings.getHunterLives();
        int newLives = currentLives;

        if (isLeftClick) {
            // 左键：减少
            newLives = Math.max(0, currentLives - 1);
            if (newLives != currentLives) {
                String livesText = newLives == 0 ? "无限" : String.valueOf(newLives);
                sendSuccess("捕猎者生命数已设置为: " + livesText);
            } else {
                sendError("捕猎者生命数已达到最小值(0=无限)！");
                playErrorSound();
                return;
            }
        } else if (isRightClick) {
            // 右键：增加
            newLives = Math.min(10, currentLives + 1);
            if (newLives != currentLives) {
                String livesText = newLives == 0 ? "无限" : String.valueOf(newLives);
                sendSuccess("捕猎者生命数已设置为: " + livesText);
            } else {
                sendError("捕猎者生命数已达到最大值(10)！");
                playErrorSound();
                return;
            }
        }

        settings.setHunterLives(newLives);
        refresh();
    }

    /**
     * 处理捕猎者生命数的聊天输入
     */
    private void handleHunterLivesChatInput() {
        close();

        plugin.getGuiManager().getChatInputManager().startChatInput(
            player,
            "请输入捕猎者生命数（0表示无限，1-10）：",
            input -> {
                try {
                    int lives = Integer.parseInt(input);
                    if (lives < 0 || lives > 10) {
                        ComponentUtil.sendMessage(player, ComponentUtil.error("生命数必须在0-10之间！"));
                    } else {
                        settings.setHunterLives(lives);
                        String livesText = lives == 0 ? "无限" : String.valueOf(lives);
                        ComponentUtil.sendMessage(player, ComponentUtil.success("捕猎者生命数已设置为: " + livesText));
                    }
                } catch (NumberFormatException e) {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("请输入有效的数字！"));
                }

                // 重新打开GUI - 从RoomManager获取最新的房间对象
                Room currentRoom = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
                if (currentRoom != null) {
                    plugin.getGuiManager().openRoomSettingsGui(player, currentRoom);
                } else {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("房间不存在或已被删除"));
                    plugin.getGuiManager().openRoomListGui(player);
                }
            },
            () -> {
                // 取消时重新打开GUI - 从RoomManager获取最新的房间对象
                Room currentRoom = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
                if (currentRoom != null) {
                    plugin.getGuiManager().openRoomSettingsGui(player, currentRoom);
                } else {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("房间不存在或已被删除"));
                    plugin.getGuiManager().openRoomListGui(player);
                }
            }
        );
    }

    /**
     * 获取无法开始游戏的原因
     */
    private String getCannotStartReason() {
        if (room == null) {
            return "房间不存在";
        }

        if (!room.getGameState().canStart()) {
            return "游戏状态不允许开始";
        }

        int playerCount = room.getPlayerCount();
        if (playerCount < room.getType().getMinPlayers()) {
            return "玩家数量不足（需要至少" + room.getType().getMinPlayers() + "人）";
        }

        int speedrunners = room.getPlayerCount(com.projectSource.ultimateManhurt.game.PlayerRole.SPEEDRUNNER);
        int hunters = room.getPlayerCount(com.projectSource.ultimateManhurt.game.PlayerRole.HUNTER);

        if (speedrunners == 0) {
            return "需要至少1个速通者";
        }

        if (hunters == 0) {
            return "需要至少1个捕猎者";
        }

        return "未知原因";
    }

    /**
     * 处理开始游戏
     */
    private void handleStartGame() {
        if (room == null) {
            sendError("房间不存在！");
            playErrorSound();
            return;
        }

        if (!room.canStartGame()) {
            sendError("无法开始游戏：" + getCannotStartReason());
            playErrorSound();
            return;
        }

        // 显示进度消息并关闭GUI
        sendMessage("正在创建游戏世界，这可能需要几分钟时间...");
        sendMessage("请耐心等待，不要关闭游戏！");
        close(); // 关闭GUI以避免重复点击

        // 创建游戏会话并开始游戏
        plugin.getGameManager().createGameSession(room)
            .thenAccept(gameSession -> {
                // 在主线程中执行
                org.bukkit.Bukkit.getScheduler().runTask(plugin, () -> {
                    try {
                        com.projectSource.ultimateManhurt.util.ComponentUtil.sendMessage(
                            player,
                            com.projectSource.ultimateManhurt.util.ComponentUtil.success("游戏世界创建完成！正在启动游戏...")
                        );

                        boolean started = gameSession.startGame();
                        if (started) {
                            com.projectSource.ultimateManhurt.util.ComponentUtil.sendMessage(
                                player,
                                com.projectSource.ultimateManhurt.util.ComponentUtil.success("游戏开始！祝你好运！")
                            );
                        } else {
                            com.projectSource.ultimateManhurt.util.ComponentUtil.sendMessage(
                                player,
                                com.projectSource.ultimateManhurt.util.ComponentUtil.error("开始游戏失败！")
                            );
                        }
                    } catch (Exception e) {
                        com.projectSource.ultimateManhurt.util.ComponentUtil.sendMessage(
                            player,
                            com.projectSource.ultimateManhurt.util.ComponentUtil.error("启动游戏时发生错误：" + e.getMessage())
                        );
                        plugin.getLogger().severe("启动游戏失败: " + e.getMessage());
                    }
                });
            })
            .exceptionally(throwable -> {
                // 在主线程中处理异常
                org.bukkit.Bukkit.getScheduler().runTask(plugin, () -> {
                    com.projectSource.ultimateManhurt.util.ComponentUtil.sendMessage(
                        player,
                        com.projectSource.ultimateManhurt.util.ComponentUtil.error("创建游戏世界失败：" + throwable.getMessage())
                    );
                    com.projectSource.ultimateManhurt.util.ComponentUtil.sendMessage(
                        player,
                        com.projectSource.ultimateManhurt.util.ComponentUtil.info("请稍后再试，或联系管理员")
                    );
                    plugin.getLogger().severe("创建游戏会话失败: " + throwable.getMessage());
                    throwable.printStackTrace();
                });
                return null;
            });
    }

    /**
     * 处理速通者人数限制设置
     */
    private void handleMaxSpeedrunners(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
        if (currentOperationMode == OperationMode.MOUSE) {
            // 鼠标操作模式
            if (isLeftClick || isRightClick) {
                handleMaxSpeedrunnersMouseOperation(isLeftClick, isRightClick);
            }
        } else {
            // 聊天输入模式
            handleMaxSpeedrunnersChatInput();
        }
    }

    /**
     * 处理速通者人数限制的鼠标操作
     */
    private void handleMaxSpeedrunnersMouseOperation(boolean isLeftClick, boolean isRightClick) {
        int currentMax = settings.getMaxSpeedrunners();
        int newMax = currentMax;

        if (isLeftClick) {
            // 左键：减少
            newMax = Math.max(1, currentMax - 1);
            if (newMax != currentMax) {
                sendSuccess("速通者最大人数已设置为: " + newMax + " 人");
            } else {
                sendError("速通者人数已达到最小值(1人)！");
                playErrorSound();
                return;
            }
        } else if (isRightClick) {
            // 右键：增加
            newMax = Math.min(10, currentMax + 1);
            if (newMax != currentMax) {
                sendSuccess("速通者最大人数已设置为: " + newMax + " 人");
            } else {
                sendError("速通者人数已达到最大值(10人)！");
                playErrorSound();
                return;
            }
        }

        settings.setMaxSpeedrunners(newMax);

        // 检查当前速通者人数是否超限
        int currentSpeedrunners = room.getPlayerCount(com.projectSource.ultimateManhurt.game.PlayerRole.SPEEDRUNNER);
        if (currentSpeedrunners > newMax) {
            sendInfo("当前速通者人数(" + currentSpeedrunners + ")超过新限制，请手动调整");
        }

        refresh();
    }

    /**
     * 处理速通者人数限制的聊天输入
     */
    private void handleMaxSpeedrunnersChatInput() {
        close();

        plugin.getGuiManager().getChatInputManager().startChatInput(
            player,
            "请输入速通者最大人数（1-10）：",
            input -> {
                try {
                    int maxSpeedrunners = Integer.parseInt(input);
                    if (maxSpeedrunners < 1 || maxSpeedrunners > 10) {
                        ComponentUtil.sendMessage(player, ComponentUtil.error("速通者人数必须在1-10之间！"));
                    } else {
                        settings.setMaxSpeedrunners(maxSpeedrunners);
                        ComponentUtil.sendMessage(player, ComponentUtil.success("速通者最大人数已设置为 " + maxSpeedrunners));

                        // 检查当前速通者人数是否超限
                        int currentSpeedrunners = room.getPlayerCount(com.projectSource.ultimateManhurt.game.PlayerRole.SPEEDRUNNER);
                        if (currentSpeedrunners > maxSpeedrunners) {
                            ComponentUtil.sendMessage(player, ComponentUtil.warning(
                                "当前速通者人数(" + currentSpeedrunners + ")超过新限制，请手动调整"));
                        }
                    }
                } catch (NumberFormatException e) {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("请输入有效的数字！"));
                }

                // 重新打开GUI
                Room currentRoom = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
                if (currentRoom != null) {
                    plugin.getGuiManager().openRoomSettingsGui(player, currentRoom);
                } else {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("房间不存在或已被删除"));
                    plugin.getGuiManager().openRoomListGui(player);
                }
            },
            () -> {
                // 取消回调
                Room currentRoom = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
                if (currentRoom != null) {
                    plugin.getGuiManager().openRoomSettingsGui(player, currentRoom);
                } else {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("房间不存在或已被删除"));
                    plugin.getGuiManager().openRoomListGui(player);
                }
            });
    }

    /**
     * 处理捕猎者人数限制设置
     */
    private void handleMaxHunters(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
        if (currentOperationMode == OperationMode.MOUSE) {
            // 鼠标操作模式
            if (isLeftClick || isRightClick) {
                handleMaxHuntersMouseOperation(isLeftClick, isRightClick);
            }
        } else {
            // 聊天输入模式
            handleMaxHuntersChatInput();
        }
    }

    /**
     * 处理捕猎者人数限制的鼠标操作
     */
    private void handleMaxHuntersMouseOperation(boolean isLeftClick, boolean isRightClick) {
        int currentMax = settings.getMaxHunters();
        int newMax = currentMax;

        if (isLeftClick) {
            // 左键：减少
            newMax = Math.max(1, currentMax - 1);
            if (newMax != currentMax) {
                sendSuccess("捕猎者最大人数已设置为: " + newMax + " 人");
            } else {
                sendError("捕猎者人数已达到最小值(1人)！");
                playErrorSound();
                return;
            }
        } else if (isRightClick) {
            // 右键：增加
            newMax = Math.min(20, currentMax + 1);
            if (newMax != currentMax) {
                sendSuccess("捕猎者最大人数已设置为: " + newMax + " 人");
            } else {
                sendError("捕猎者人数已达到最大值(20人)！");
                playErrorSound();
                return;
            }
        }

        settings.setMaxHunters(newMax);

        // 检查当前捕猎者人数是否超限
        int currentHunters = room.getPlayerCount(com.projectSource.ultimateManhurt.game.PlayerRole.HUNTER);
        if (currentHunters > newMax) {
            sendInfo("当前捕猎者人数(" + currentHunters + ")超过新限制，请手动调整");
        }

        refresh();
    }

    /**
     * 处理捕猎者人数限制的聊天输入
     */
    private void handleMaxHuntersChatInput() {
        close();

        plugin.getGuiManager().getChatInputManager().startChatInput(
            player,
            "请输入捕猎者最大人数（1-20）：",
            input -> {
                try {
                    int maxHunters = Integer.parseInt(input);
                    if (maxHunters < 1 || maxHunters > 20) {
                        ComponentUtil.sendMessage(player, ComponentUtil.error("捕猎者人数必须在1-20之间！"));
                    } else {
                        settings.setMaxHunters(maxHunters);
                        ComponentUtil.sendMessage(player, ComponentUtil.success("捕猎者最大人数已设置为 " + maxHunters));

                        // 检查当前捕猎者人数是否超限
                        int currentHunters = room.getPlayerCount(com.projectSource.ultimateManhurt.game.PlayerRole.HUNTER);
                        if (currentHunters > maxHunters) {
                            ComponentUtil.sendMessage(player, ComponentUtil.warning(
                                "当前捕猎者人数(" + currentHunters + ")超过新限制，请手动调整"));
                        }
                    }
                } catch (NumberFormatException e) {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("请输入有效的数字！"));
                }

                // 重新打开GUI
                Room currentRoom = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
                if (currentRoom != null) {
                    plugin.getGuiManager().openRoomSettingsGui(player, currentRoom);
                } else {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("房间不存在或已被删除"));
                    plugin.getGuiManager().openRoomListGui(player);
                }
            },
            () -> {
                // 取消回调
                Room currentRoom = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
                if (currentRoom != null) {
                    plugin.getGuiManager().openRoomSettingsGui(player, currentRoom);
                } else {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("房间不存在或已被删除"));
                    plugin.getGuiManager().openRoomListGui(player);
                }
            });
    }

    /**
     * 处理速通者血量设置
     */
    private void handleSpeedrunnerHealth(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
        if (currentOperationMode == OperationMode.MOUSE) {
            // 鼠标操作模式
            if (isLeftClick || isRightClick) {
                handleSpeedrunnerHealthMouseOperation(isLeftClick, isRightClick, isShiftClick);
            }
        } else {
            // 聊天输入模式
            handleSpeedrunnerHealthChatInput();
        }
    }

    /**
     * 处理速通者血量的鼠标操作
     */
    private void handleSpeedrunnerHealthMouseOperation(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
        double currentHealth = settings.getSpeedrunnerMaxHealth();
        double newHealth = currentHealth;

        if (isLeftClick) {
            // 左键：减少
            double decrease = isShiftClick ? 10.0 : 2.0;
            newHealth = Math.max(1.0, currentHealth - decrease);
            if (newHealth != currentHealth) {
                sendSuccess("速通者血量已设置为: " + String.format("%.0f", newHealth) + " 血");
            } else {
                sendError("速通者血量已达到最小值(1血)！");
                playErrorSound();
                return;
            }
        } else if (isRightClick) {
            // 右键：增加
            double increase = isShiftClick ? 10.0 : 2.0;
            newHealth = Math.min(100.0, currentHealth + increase);
            if (newHealth != currentHealth) {
                sendSuccess("速通者血量已设置为: " + String.format("%.0f", newHealth) + " 血");
            } else {
                sendError("速通者血量已达到最大值(100血)！");
                playErrorSound();
                return;
            }
        }

        settings.setSpeedrunnerMaxHealth(newHealth);
        refresh();
    }

    /**
     * 处理速通者血量的聊天输入
     */
    private void handleSpeedrunnerHealthChatInput() {
        close();

        plugin.getGuiManager().getChatInputManager().startChatInput(
            player,
            "请输入速通者血量（1-100）：",
            input -> {
                try {
                    double health = Double.parseDouble(input);
                    if (health < 1.0 || health > 100.0) {
                        ComponentUtil.sendMessage(player, ComponentUtil.error("血量必须在1-100之间！"));
                    } else {
                        settings.setSpeedrunnerMaxHealth(health);
                        ComponentUtil.sendMessage(player, ComponentUtil.success("速通者血量已设置为: " + String.format("%.0f", health) + " 血"));
                    }
                } catch (NumberFormatException e) {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("请输入有效的数字！"));
                }

                // 重新打开GUI
                Room currentRoom = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
                if (currentRoom != null) {
                    plugin.getGuiManager().openRoomSettingsGui(player, currentRoom);
                } else {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("房间不存在或已被删除"));
                    plugin.getGuiManager().openRoomListGui(player);
                }
            },
            // 取消回调
            () -> {
                Room currentRoom = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
                if (currentRoom != null) {
                    plugin.getGuiManager().openRoomSettingsGui(player, currentRoom);
                } else {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("房间不存在或已被删除"));
                    plugin.getGuiManager().openRoomListGui(player);
                }
            });
    }

    /**
     * 处理捕猎者血量设置
     */
    private void handleHunterHealth(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
        if (currentOperationMode == OperationMode.MOUSE) {
            // 鼠标操作模式
            if (isLeftClick || isRightClick) {
                handleHunterHealthMouseOperation(isLeftClick, isRightClick, isShiftClick);
            }
        } else {
            // 聊天输入模式
            handleHunterHealthChatInput();
        }
    }

    /**
     * 处理捕猎者血量的鼠标操作
     */
    private void handleHunterHealthMouseOperation(boolean isLeftClick, boolean isRightClick, boolean isShiftClick) {
        double currentHealth = settings.getHunterMaxHealth();
        double newHealth = currentHealth;

        if (isLeftClick) {
            // 左键：减少
            double decrease = isShiftClick ? 10.0 : 2.0;
            newHealth = Math.max(1.0, currentHealth - decrease);
            if (newHealth != currentHealth) {
                sendSuccess("捕猎者血量已设置为: " + String.format("%.0f", newHealth) + " 血");
            } else {
                sendError("捕猎者血量已达到最小值(1血)！");
                playErrorSound();
                return;
            }
        } else if (isRightClick) {
            // 右键：增加
            double increase = isShiftClick ? 10.0 : 2.0;
            newHealth = Math.min(100.0, currentHealth + increase);
            if (newHealth != currentHealth) {
                sendSuccess("捕猎者血量已设置为: " + String.format("%.0f", newHealth) + " 血");
            } else {
                sendError("捕猎者血量已达到最大值(100血)！");
                playErrorSound();
                return;
            }
        }

        settings.setHunterMaxHealth(newHealth);
        refresh();
    }

    /**
     * 处理捕猎者血量的聊天输入
     */
    private void handleHunterHealthChatInput() {
        close();

        plugin.getGuiManager().getChatInputManager().startChatInput(
            player,
            "请输入捕猎者血量（1-100）：",
            input -> {
                try {
                    double health = Double.parseDouble(input);
                    if (health < 1.0 || health > 100.0) {
                        ComponentUtil.sendMessage(player, ComponentUtil.error("血量必须在1-100之间！"));
                    } else {
                        settings.setHunterMaxHealth(health);
                        ComponentUtil.sendMessage(player, ComponentUtil.success("捕猎者血量已设置为: " + String.format("%.0f", health) + " 血"));
                    }
                } catch (NumberFormatException e) {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("请输入有效的数字！"));
                }

                // 重新打开GUI
                Room currentRoom = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
                if (currentRoom != null) {
                    plugin.getGuiManager().openRoomSettingsGui(player, currentRoom);
                } else {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("房间不存在或已被删除"));
                    plugin.getGuiManager().openRoomListGui(player);
                }
            },
            // 取消回调
            () -> {
                Room currentRoom = plugin.getRoomManager().getRoomByPlayer(player.getUniqueId());
                if (currentRoom != null) {
                    plugin.getGuiManager().openRoomSettingsGui(player, currentRoom);
                } else {
                    ComponentUtil.sendMessage(player, ComponentUtil.error("房间不存在或已被删除"));
                    plugin.getGuiManager().openRoomListGui(player);
                }
            });
    }
}
