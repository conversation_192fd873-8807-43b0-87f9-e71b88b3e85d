package com.projectSource.ultimateManhurt.listener;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.GameSession;
import com.projectSource.ultimateManhurt.profession.Profession;
import com.projectSource.ultimateManhurt.room.Room;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;

import java.util.UUID;

/**
 * 玩家事件监听器
 */
public class PlayerListener implements Listener {

    private final UltimateManhurt plugin;

    public PlayerListener(UltimateManhurt plugin) {
        this.plugin = plugin;
    }

    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();

        // 发送欢迎消息
        ComponentUtil.sendMessage(player, ComponentUtil.parse(
            "<gradient:gold:yellow><bold>欢迎来到 Ultimate Manhunt!</bold></gradient>"
        ));
        ComponentUtil.sendMessage(player, ComponentUtil.parse(
            "<gray>使用 <aqua>/manhunt help <gray>查看帮助信息"
        ));

        // 检查玩家是否在游戏中（处理重连）
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(playerId);
        if (gameSession != null) {
            // 玩家重连到游戏中
            ComponentUtil.sendMessage(player, ComponentUtil.info("检测到你正在游戏中，正在恢复..."));

            // 恢复技能Boss Bar和被动技能（如果启用了职业系统）
            if (gameSession.getRoom().getSettings().isProfessionSystemEnabled()) {
                plugin.getProfessionManager().getSkillBossBarManager().onPlayerJoin(player);

                // 重新启动玩家的被动技能
                restorePlayerPassiveSkills(player);
            }

            // 尝试恢复玩家位置
            boolean locationRestored = plugin.getPlayerLocationManager().restorePlayerLocation(player);
            if (!locationRestored) {
                // 如果无法恢复位置，传送到游戏世界出生点
                plugin.getWorldManager().teleportToGameWorld(player, gameSession.getRoom().getId());
                ComponentUtil.sendMessage(player, ComponentUtil.warning("无法恢复你的位置，已传送到出生点"));
            } else {
                ComponentUtil.sendMessage(player, ComponentUtil.success("已恢复到断线前的位置"));
            }

            // 创建计分板
            plugin.getScoreboardManager().createGameScoreboard(player, gameSession);

            ComponentUtil.sendMessage(player, ComponentUtil.success("已恢复到游戏中！"));


        }



        // 记录玩家数据
        plugin.getDataManager().loadPlayerData(playerId);

        plugin.getLogger().info("玩家 " + player.getName() + " 加入了服务器");
    }

    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();

        // 清理技能Boss Bar
        plugin.getProfessionManager().getSkillBossBarManager().onPlayerQuit(player);

        // 检查玩家是否在房间中
        Room room = plugin.getRoomManager().getRoomByPlayer(playerId);
        if (room != null) {
            // 检查是否在游戏中
            GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(playerId);
            boolean inActiveGame = gameSession != null && gameSession.getState().isActive();

            if (room.isOwner(playerId)) {
                // 房主离开的处理
                if (inActiveGame) {
                    // 游戏进行中，房主只是断线，不转移房主，只通知其他玩家
                    notifyRoomPlayers(room, player.getName() + " (房主) 离线了");
                    plugin.getLogger().info("房主 " + player.getName() + " 在游戏中离线，保留房主身份");
                } else {
                    // 游戏未进行，可以转移房主
                    handleOwnerLeave(room, player);
                }
            } else {
                if (!inActiveGame) {
                    // 不在游戏中，普通玩家离开房间
                    plugin.getRoomManager().forceLeaveRoom(playerId);
                    notifyRoomPlayers(room, player.getName() + " 离开了房间");
                } else {
                    // 在游戏中，只通知离线，不移出房间
                    notifyRoomPlayers(room, player.getName() + " 离线了");
                }
            }


        }

        // 检查玩家是否在游戏中
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(playerId);
        if (gameSession != null) {
            // 玩家在游戏中离开，保存位置
            plugin.getPlayerLocationManager().savePlayerLocation(player);
            plugin.getLogger().info("玩家 " + player.getName() + " 在游戏中离线，已保存位置");

            // 如果游戏正在进行，给其他玩家发送通知
            if (gameSession.getState().isActive()) {
                notifyGamePlayers(gameSession, player.getName() + " 离线了");
            }
        }

        // 关闭GUI
        plugin.getGuiManager().closeGui(player);

        // 移除计分板
        plugin.getScoreboardManager().removeScoreboard(player);

        // 保存玩家数据
        plugin.getDataManager().savePlayerData(playerId);

        plugin.getLogger().info("玩家 " + player.getName() + " 离开了服务器");
    }

    /**
     * 恢复玩家的被动技能（重连时使用）
     */
    private void restorePlayerPassiveSkills(Player player) {
        UUID playerId = player.getUniqueId();
        Profession profession = plugin.getProfessionManager().getPlayerProfession(playerId);

        if (profession == null) {
            return;
        }

        // 根据职业重新启动相应的被动技能
        switch (profession) {
            case LENA:
                // 重新启动莱娜的森之祝福光环
                plugin.getProfessionManager().getPassiveSkillHandler().startLenaForestBlessing(player);
                break;

            case NIGHT_LORD:
                // 重新启动暗夜领主的夜晚检测
                plugin.getProfessionManager().getPassiveSkillHandler().startNightLordNightDetection(player);
                break;

            case SHAMAN:
                // 萨满的狗在游戏中应该已经存在，不需要特殊的重连处理
                break;

            default:
                // 其他职业暂时不需要特殊处理
                break;
        }
    }

    /**
     * 处理房主离开（仅在游戏未进行时调用）
     * 游戏进行中的房主断线不会触发此方法，保留房主身份以便重连
     */
    private void handleOwnerLeave(Room room, Player owner) {
        plugin.getLogger().info("处理房主 " + owner.getName() + " 离开房间 " + room.getName() + "（游戏未进行）");
        // 尝试转移房主给其他玩家
        UUID newOwnerId = room.getPlayers().stream()
                .filter(id -> !id.equals(owner.getUniqueId()))
                .findFirst()
                .orElse(null);

        if (newOwnerId != null) {
            // 转移房主
            Player newOwner = plugin.getServer().getPlayer(newOwnerId);
            if (newOwner != null && room.transferOwner(newOwnerId)) {
                // 先让原房主强制离开房间（断线情况）
                plugin.getRoomManager().forceLeaveRoom(owner.getUniqueId());

                // 通知房间内所有玩家
                notifyRoomPlayers(room, owner.getName() + " 离开了，" + newOwner.getName() + " 成为了新房主");
                ComponentUtil.sendMessage(newOwner, ComponentUtil.info("你已成为房间 " + room.getName() + " 的新房主"));

                plugin.getLogger().info("房间 " + room.getName() + " 的房主从 " + owner.getName() + " 转移到 " + newOwner.getName());
            } else {
                // 转移失败，解散房间
                notifyRoomPlayers(room, "房主转移失败，房间即将解散");
                plugin.getRoomManager().deleteRoom(room.getId());
            }
        } else {
            // 没有其他玩家，解散房间
            notifyRoomPlayers(room, "房主离开，房间即将解散");
            plugin.getRoomManager().deleteRoom(room.getId());
        }
    }

    /**
     * 通知房间内的玩家
     */
    private void notifyRoomPlayers(Room room, String message) {
        for (UUID playerId : room.getPlayers()) {
            Player player = plugin.getServer().getPlayer(playerId);
            if (player != null && player.isOnline()) {
                ComponentUtil.sendMessage(player, ComponentUtil.info(message));
            }
        }
    }

    /**
     * 通知游戏中的玩家
     */
    private void notifyGamePlayers(GameSession gameSession, String message) {
        for (UUID playerId : gameSession.getPlayerRoles().keySet()) {
            Player player = plugin.getServer().getPlayer(playerId);
            if (player != null && player.isOnline()) {
                ComponentUtil.sendMessage(player, ComponentUtil.warning(message));
            }
        }
    }
}
