package com.projectSource.ultimateManhurt.profession.skill;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.GameSession;
import com.projectSource.ultimateManhurt.game.PlayerRole;
import com.projectSource.ultimateManhurt.profession.Profession;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.UUID;

/**
 * 主动技能处理器
 */
public class ActiveSkillHandler {

    /**
     * 玩家时光状态数据类
     */
    public static class PlayerTimeState {
        private final Map<UUID, PlayerSnapshot> playerSnapshots = new HashMap<>();
        private final long timestamp;

        public PlayerTimeState() {
            this.timestamp = System.currentTimeMillis();
        }

        public void addPlayerSnapshot(UUID playerId, PlayerSnapshot snapshot) {
            playerSnapshots.put(playerId, snapshot);
        }

        public PlayerSnapshot getPlayerSnapshot(UUID playerId) {
            return playerSnapshots.get(playerId);
        }

        public Set<UUID> getPlayerIds() {
            return playerSnapshots.keySet();
        }

        public long getTimestamp() {
            return timestamp;
        }
    }

    /**
     * 玩家快照数据类
     */
    public static class PlayerSnapshot {
        private final Location location;
        private final double health;
        private final int foodLevel;
        private final ItemStack[] inventory;
        private final ItemStack[] armor;
        private final ItemStack offHand;

        public PlayerSnapshot(Player player) {
            this.location = player.getLocation().clone();
            this.health = player.getHealth();
            this.foodLevel = player.getFoodLevel();
            this.inventory = player.getInventory().getContents().clone();
            this.armor = player.getInventory().getArmorContents().clone();
            this.offHand = player.getInventory().getItemInOffHand().clone();
        }

        public void restoreToPlayer(Player player) {
            player.teleport(location);
            double maxHealth = player.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).getValue();
            player.setHealth(Math.min(health, maxHealth));
            player.setFoodLevel(foodLevel);
            player.getInventory().setContents(inventory);
            player.getInventory().setArmorContents(armor);
            player.getInventory().setItemInOffHand(offHand);
        }
    }

    /**
     * 世界时间记录类
     */
    private static class WorldTimeRecord {
        private final long originalTime;
        private int activeNightLords;

        public WorldTimeRecord(long originalTime) {
            this.originalTime = originalTime;
            this.activeNightLords = 1;
        }

        public long getOriginalTime() {
            return originalTime;
        }

        public int getActiveNightLords() {
            return activeNightLords;
        }

        public void incrementActiveNightLords() {
            this.activeNightLords++;
        }

        public void decrementActiveNightLords() {
            this.activeNightLords--;
        }

        public boolean hasActiveNightLords() {
            return activeNightLords > 0;
        }
    }

    private final UltimateManhurt plugin;
    
    // 骷髅巫妖诅咒状态 <玩家UUID, 是否开启>
    private final Map<UUID, Boolean> lichCurseStates = new HashMap<>();
    
    // 猎人战斗专注状态 <玩家UUID, 结束时间>
    private final Map<UUID, Long> combatFocusStates = new HashMap<>();

    // 暗影刺客隐身状态 <玩家UUID, 结束时间>
    private final Map<UUID, Long> shadowInvisibilityStates = new HashMap<>();

    // 蜘蛛跳跃状态 <玩家UUID, 跳跃开始时间>
    private final Map<UUID, Long> spiderLeapStates = new HashMap<>();

    // 末影人最后受攻击时间 <玩家UUID, 受攻击时间>
    private final Map<UUID, Long> endermanLastDamageTime = new HashMap<>();

    // 萨满龙卷风暴无敌状态 <玩家UUID, 结束时间>
    private final Map<UUID, Long> shamanInvulnerableStates = new HashMap<>();

    // 恐惧魔王影压连击次数 <玩家UUID, 连击次数>
    private final Map<UUID, Integer> fearLordComboCount = new HashMap<>();

    // 船长海灵诅咒状态 <船长UUID, 结束时间>
    private final Map<UUID, Long> captainSeaCurseStates = new HashMap<>();

    // 僵尸回光返照状态 <僵尸UUID, 结束时间>
    private final Map<UUID, Long> zombieLastStandStates = new HashMap<>();

    // 莱娜钩爪免疫摔落伤害状态 <玩家UUID, 结束时间>
    private final Map<UUID, Long> lenaFallDamageImmunity = new HashMap<>();

    // 机器人地雷系统
    private final Map<UUID, Set<Location>> robotTraps = new HashMap<>();
    private final Map<Location, UUID> trapOwners = new HashMap<>();
    private final Map<Location, Long> trapTriggerTimes = new HashMap<>();
    private final Map<UUID, Integer> trapComboCount = new HashMap<>();

    // 女巫巫毒诅咒系统
    private final Map<UUID, Long> voodooAffectedPlayers = new HashMap<>();

    // 方源春秋必成状态管理
    private final Map<UUID, Boolean> fangyuanTimeRewindActive = new HashMap<>(); // 是否已创建时光分身
    private final Map<String, PlayerTimeState> gameTimeStates = new HashMap<>(); // 游戏会话的时光状态
    private final Map<UUID, Long> fangyuanLastCreateTime = new HashMap<>(); // 上次创建时光分身的时间

    // 暗夜领主暗月升起状态 <玩家UUID, 结束时间>
    private final Map<UUID, Long> nightLordDarkMoonStates = new HashMap<>();

    // 暗夜领主世界时间记录 <游戏会话ID, 原始时间信息>
    private final Map<String, WorldTimeRecord> worldTimeRecords = new HashMap<>();
    
    public ActiveSkillHandler(UltimateManhurt plugin) {
        this.plugin = plugin;
    }
    
    /**
     * 执行主动技能
     */
    public boolean executeActiveSkill(Player player, Profession profession) {
        return switch (profession) {
            case ENDERMAN -> handleEndermanTeleport(player);
            case BUTCHER -> handleButcherHook(player);
            case SKELETON -> handleSkeletonLichCurse(player);
            case EXPLORER -> handleExplorerRapidMining(player);
            case IRON_GOLEM -> handleIronGolemHealingAura(player);
            case HUNTER -> handleHunterCombatFocus(player);
            case SHADOW_ASSASSIN -> handleShadowAssassinInvisibility(player);
            case SPIDER -> handleSpiderLeap(player);
            case PIGLIN -> handlePiglinRally(player);
            case SHAMAN -> handleShamanTornado(player);
            case FEAR_LORD -> handleFearLordShadowStrike(player);
            case LENA -> handleLenaGrappleHook(player);
            case CAPTAIN -> handleCaptainSeaCurse(player);
            case ZOMBIE -> handleZombieLastStand(player);
            case ROBOT -> handleRobotTrap(player);
            case WITCH -> handleWitchVoodooCurse(player);
            case FANGYUAN -> handleFangyuanTimeRewind(player);
            case NIGHT_LORD -> handleNightLordDarkMoon(player);
        };
    }
    
    /**
     * 末影人传送技能 - 新版本
     * 当任意的剑在副手时右键，可以闪烁到前方一段距离(指针指向位置，无论距离远近并且不会传送到方块里面)。
     * 如果在前方位置有玩家则会闪烁到该名玩家背后并获得速度三效果。但是受到攻击的3s内无法使用
     */
    private boolean handleEndermanTeleport(Player player) {
        UUID playerId = player.getUniqueId();

        // 检查是否在受攻击后的3秒内
        if (!canEndermanUseTeleport(playerId)) {
            Long lastDamageTime = endermanLastDamageTime.get(playerId);
            long timeSinceLastDamage = System.currentTimeMillis() - lastDamageTime;
            long remainingTime = 3000 - timeSinceLastDamage;
            ComponentUtil.sendMessage(player, ComponentUtil.error("受到攻击后无法使用传送！剩余限制时间: " + (remainingTime / 1000 + 1) + "秒"));
            return false;
        }

        Location playerLoc = player.getEyeLocation();

        // 使用射线追踪找到玩家指针指向的位置 - 无距离限制
        org.bukkit.util.RayTraceResult rayTrace = player.getWorld().rayTraceBlocks(
            playerLoc,
            playerLoc.getDirection(),
            1000.0, // 极大距离，实际上无限制
            org.bukkit.FluidCollisionMode.NEVER,
            true // 忽略可穿透方块
        );

        Location targetLocation;
        if (rayTrace != null && rayTrace.getHitBlock() != null) {
            // 找到了方块，智能选择传送位置
            org.bukkit.block.Block hitBlock = rayTrace.getHitBlock();
            int x = hitBlock.getX();
            int z = hitBlock.getZ();

            // 检查玩家是否在洞穴中
            if (isPlayerInCave(player)) {
                // 在洞穴中：传送到击中方块前面的安全位置，而不是地表
                targetLocation = findCaveTeleportLocation(playerLoc, rayTrace);
                plugin.getLogger().info("末影人在洞穴中传送到方块前面的安全位置");
            } else {
                // 在地表：传送到该XZ坐标的最高点
                int highestY = player.getWorld().getHighestBlockYAt(x, z);
                targetLocation = new Location(player.getWorld(), x + 0.5, highestY + 1, z + 0.5);
                plugin.getLogger().info("末影人传送到方块 (" + x + ", " + highestY + ", " + z + ") 的最高点");
            }
        } else {
            // 没有找到方块，传送到视线方向的远处
            targetLocation = playerLoc.add(playerLoc.getDirection().multiply(100));
            // 获取该位置的最高点
            int x = targetLocation.getBlockX();
            int z = targetLocation.getBlockZ();
            int highestY = player.getWorld().getHighestBlockYAt(x, z);
            targetLocation = new Location(player.getWorld(), x + 0.5, highestY + 1, z + 0.5);
        }

        // 检查目标位置附近是否有玩家
        Player nearbyPlayer = findNearbyPlayer(player, targetLocation, 5.0);

        Location teleportLoc;
        if (nearbyPlayer != null) {
            // 传送到目标玩家背后
            Vector targetDirection = nearbyPlayer.getLocation().getDirection().normalize();
            teleportLoc = nearbyPlayer.getLocation().subtract(targetDirection.multiply(2));
            teleportLoc.setY(nearbyPlayer.getLocation().getY());

            // 给予速度三效果
            player.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, 100, 2)); // 5秒

            ComponentUtil.sendMessage(player, ComponentUtil.info("传送到 " + nearbyPlayer.getName() + " 背后！"));
        } else {
            // 直接传送到目标位置（不检查安全性）
            teleportLoc = targetLocation;
            ComponentUtil.sendMessage(player, ComponentUtil.info("传送到指针指向位置！"));
        }

        // 执行传送
        player.teleport(teleportLoc);
        player.playSound(player.getLocation(), Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.0f);

        // 冷却时间由ProfessionManager.useActiveSkill()统一处理

        return true;
    }
    
    /**
     * 屠夫钩子技能
     * 当任意的剑在副手时右键，会向前方一定距离放出一条钩子。如果有玩家都会被拉到你的身边
     */
    private boolean handleButcherHook(Player player) {
        Location playerLoc = player.getLocation();
        Vector direction = playerLoc.getDirection();

        // 增加搜索距离到20格
        double hookRange = 20.0;

        // 显示钩子轨迹粒子效果
        showHookTrajectory(player, direction, hookRange);

        // 寻找前方的玩家
        Player targetPlayer = findPlayerInDirection(player, direction, hookRange);

        if (targetPlayer == null) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("钩子没有命中任何玩家"));
            return false;
        }
        
        // 将目标玩家拉到身边
        Location pullLocation = playerLoc.clone().add(direction.multiply(2));
        pullLocation.setY(playerLoc.getY());

        targetPlayer.teleport(pullLocation);

        // 检查是否为队友
        com.projectSource.ultimateManhurt.game.GameSession gameSession =
            plugin.getGameManager().getGameSessionByPlayer(player.getUniqueId());

        if (gameSession != null) {
            com.projectSource.ultimateManhurt.game.PlayerRole targetRole =
                gameSession.getPlayerRole(targetPlayer.getUniqueId());
            com.projectSource.ultimateManhurt.game.PlayerRole playerRole =
                gameSession.getPlayerRole(player.getUniqueId());

            // 检查是否为队友（同阵营）
            boolean isTeammate = (playerRole == targetRole && playerRole != null);

            if (!isTeammate) {
                // 不是队友，施加缓慢效果和跳跃禁用
                targetPlayer.addPotionEffect(new PotionEffect(
                    PotionEffectType.SLOWNESS, 60, 9)); // 3秒 = 60 tick, 缓慢十 = 等级9

                // 禁用目标玩家3秒跳跃，防止跳跃抵消技能效果
                plugin.getProfessionManager().getPassiveSkillHandler().disableJump(targetPlayer.getUniqueId(), 3000);

                // 如果使用者是捕猎者，目标是速通者，则造成伤害
                if (playerRole == com.projectSource.ultimateManhurt.game.PlayerRole.HUNTER &&
                    targetRole == com.projectSource.ultimateManhurt.game.PlayerRole.SPEEDRUNNER) {

                    double damage = 6.0; // 3颗心的伤害
                    targetPlayer.damage(damage, player); // 指定伤害来源

                    ComponentUtil.sendMessage(player, ComponentUtil.info("钩子命中 " + targetPlayer.getName() + "！造成伤害并施加缓慢效果"));
                    ComponentUtil.sendMessage(targetPlayer, ComponentUtil.warning("被 " + player.getName() + " 的钩子拉住并受到伤害和缓慢效果！"));
                } else {
                    ComponentUtil.sendMessage(player, ComponentUtil.info("钩子命中 " + targetPlayer.getName() + "！施加缓慢效果"));
                    ComponentUtil.sendMessage(targetPlayer, ComponentUtil.warning("被 " + player.getName() + " 的钩子拉住并获得缓慢效果！"));
                }
            } else {
                // 是队友，只拉过来，不施加负面效果
                ComponentUtil.sendMessage(player, ComponentUtil.info("钩子命中队友 " + targetPlayer.getName() + "！"));
                ComponentUtil.sendMessage(targetPlayer, ComponentUtil.info("被队友 " + player.getName() + " 的钩子拉住"));
            }
        } else {
            // 无法获取游戏会话，按原逻辑处理（施加缓慢效果）
            targetPlayer.addPotionEffect(new PotionEffect(
                PotionEffectType.SLOWNESS, 60, 9));
            plugin.getProfessionManager().getPassiveSkillHandler().disableJump(targetPlayer.getUniqueId(), 3000);

            ComponentUtil.sendMessage(player, ComponentUtil.info("钩子命中 " + targetPlayer.getName() + "！施加缓慢效果"));
            ComponentUtil.sendMessage(targetPlayer, ComponentUtil.warning("被 " + player.getName() + " 的钩子拉住并获得缓慢效果！"));
        }

        // 播放效果
        player.playSound(player.getLocation(), Sound.ENTITY_FISHING_BOBBER_RETRIEVE, 1.0f, 1.0f);
        targetPlayer.playSound(targetPlayer.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);
        
        return true;
    }
    
    /**
     * 骷髅巫妖诅咒技能
     * 当任意的剑在副手时右键以开关。射出的箭会扣除自身当前13%的生命值但是射出的箭会提供3s的减速二、以及5s的凋零Ⅱ效果
     */
    private boolean handleSkeletonLichCurse(Player player) {
        UUID playerId = player.getUniqueId();
        boolean currentState = lichCurseStates.getOrDefault(playerId, false);
        boolean newState = !currentState;
        
        lichCurseStates.put(playerId, newState);
        
        if (newState) {
            ComponentUtil.sendMessage(player, ComponentUtil.info("巫妖诅咒已开启！射箭将消耗生命值但附加诅咒效果"));
            player.playSound(player.getLocation(), Sound.ENTITY_WITCH_AMBIENT, 1.0f, 0.8f);
        } else {
            ComponentUtil.sendMessage(player, ComponentUtil.info("巫妖诅咒已关闭"));
            player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.0f);
        }
        
        return true;
    }
    
    /**
     * 探险家急速挖掘技能
     * 当任意的剑在副手时右键，获得急迫三效果15s
     */
    private boolean handleExplorerRapidMining(Player player) {
        player.addPotionEffect(new PotionEffect(PotionEffectType.HASTE, 300, 2)); // 15秒
        
        player.playSound(player.getLocation(), Sound.BLOCK_BEACON_ACTIVATE, 1.0f, 1.5f);
        ComponentUtil.sendMessage(player, ComponentUtil.info("急速挖掘激活！获得急迫三效果"));
        
        return true;
    }
    
    /**
     * 铁傀儡治疗光环技能
     * 当任意的剑在副手时右键，为周围5格内的友方玩家提供伤害吸收4的效果持续15s
     */
    private boolean handleIronGolemHealingAura(Player player) {
        Location playerLoc = player.getLocation();
        int healedCount = 0;

        // 首先给使用者自己添加效果
        player.addPotionEffect(new PotionEffect(PotionEffectType.ABSORPTION, 300, 3)); // 15秒
        healedCount++;

        // 寻找周围5格内的友方玩家
        for (Entity entity : playerLoc.getWorld().getNearbyEntities(playerLoc, 5, 5, 5)) {
            if (entity instanceof Player nearbyPlayer && !nearbyPlayer.equals(player)) {
                // 检查是否是友方（同一角色）
                if (isFriendlyPlayer(player, nearbyPlayer)) {
                    // 给予伤害吸收4效果15秒
                    nearbyPlayer.addPotionEffect(new PotionEffect(PotionEffectType.ABSORPTION, 300, 3)); // 15秒

                    nearbyPlayer.playSound(nearbyPlayer.getLocation(), Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.2f);
                    ComponentUtil.sendMessage(nearbyPlayer, ComponentUtil.info("受到 " + player.getName() + " 的治疗光环保护！"));

                    healedCount++;
                }
            }
        }

        // 播放效果和消息
        player.playSound(player.getLocation(), Sound.BLOCK_BEACON_ACTIVATE, 1.0f, 1.0f);

        if (healedCount == 1) {
            // 只有自己
            ComponentUtil.sendMessage(player, ComponentUtil.info("治疗光环激活！获得伤害吸收效果"));
        } else {
            // 包括其他友方
            ComponentUtil.sendMessage(player, ComponentUtil.info("治疗光环激活！为 " + healedCount + " 名玩家（包括自己）提供保护"));
        }

        return true; // 总是成功，因为至少自己会获得效果
    }
    
    /**
     * 猎人战斗专注技能
     * 当任意的剑在副手时右键，开启后近战伤害会给你提供1.5点生命回复，射箭会给你提供4.5点生命回复，持续18s
     */
    private boolean handleHunterCombatFocus(Player player) {
        UUID playerId = player.getUniqueId();
        long endTime = System.currentTimeMillis() + 18000; // 18秒
        
        combatFocusStates.put(playerId, endTime);
        
        player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 0.8f);
        ComponentUtil.sendMessage(player, ComponentUtil.info("战斗专注激活！攻击将回复生命值"));
        
        // 18秒后自动移除状态
        new BukkitRunnable() {
            @Override
            public void run() {
                combatFocusStates.remove(playerId);
                Player p = Bukkit.getPlayer(playerId);
                if (p != null && p.isOnline()) {
                    ComponentUtil.sendMessage(p, ComponentUtil.info("战斗专注效果结束"));
                }
            }
        }.runTaskLater(plugin, 360L); // 18秒 = 360 ticks
        
        return true;
    }
    
    /**
     * 寻找指定方向上的玩家
     */
    private Player findPlayerInDirection(Player player, Vector direction, double maxDistance) {
        Location start = player.getEyeLocation();
        
        for (double distance = 1; distance <= maxDistance; distance += 0.5) {
            Location checkLoc = start.clone().add(direction.clone().multiply(distance));
            
            for (Entity entity : checkLoc.getWorld().getNearbyEntities(checkLoc, 1, 1, 1)) {
                if (entity instanceof Player target && !target.equals(player)) {
                    return target;
                }
            }
        }
        
        return null;
    }
    
    /**
     * 寻找安全的传送位置
     */
    private Location findSafeTeleportLocation(Location loc) {
        if (loc.getWorld() == null) return null;
        
        // 向下寻找安全位置
        for (int y = (int) loc.getY(); y >= (int) loc.getY() - 10; y--) {
            Location testLoc = new Location(loc.getWorld(), loc.getX(), y, loc.getZ());
            
            if (isSafeLocation(testLoc)) {
                return testLoc.add(0.5, 0, 0.5);
            }
        }
        
        return null;
    }
    
    /**
     * 检查位置是否安全
     */
    private boolean isSafeLocation(Location loc) {
        if (loc.getWorld() == null) return false;
        
        return !loc.getBlock().getType().isAir() && // 脚下有方块
               loc.clone().add(0, 1, 0).getBlock().getType().isAir() && // 身体位置为空气
               loc.clone().add(0, 2, 0).getBlock().getType().isAir(); // 头部位置为空气
    }
    
    /**
     * 检查是否是友方玩家
     */
    private boolean isFriendlyPlayer(Player player1, Player player2) {
        // 通过游戏会话检查是否是同一阵营
        com.projectSource.ultimateManhurt.game.GameSession gameSession = 
            plugin.getGameManager().getGameSessionByPlayer(player1.getUniqueId());
        
        if (gameSession == null) return false;
        
        com.projectSource.ultimateManhurt.game.PlayerRole role1 = gameSession.getPlayerRole(player1.getUniqueId());
        com.projectSource.ultimateManhurt.game.PlayerRole role2 = gameSession.getPlayerRole(player2.getUniqueId());
        
        return role1 != null && role1 == role2;
    }
    
    /**
     * 获取巫妖诅咒状态
     */
    public boolean isLichCurseActive(UUID playerId) {
        return lichCurseStates.getOrDefault(playerId, false);
    }
    
    /**
     * 获取战斗专注状态
     */
    public boolean isCombatFocusActive(UUID playerId) {
        Long endTime = combatFocusStates.get(playerId);
        if (endTime == null) return false;
        
        if (System.currentTimeMillis() >= endTime) {
            combatFocusStates.remove(playerId);
            return false;
        }
        
        return true;
    }
    
    /**
     * 显示钩子轨迹粒子效果
     */
    private void showHookTrajectory(Player player, Vector direction, double maxDistance) {
        Location start = player.getEyeLocation();

        // 异步显示粒子效果，避免阻塞主线程
        org.bukkit.Bukkit.getScheduler().runTaskAsynchronously(plugin, () -> {
            for (double distance = 0.5; distance <= maxDistance; distance += 0.5) {
                Location particleLoc = start.clone().add(direction.clone().multiply(distance));

                // 在主线程中显示粒子
                org.bukkit.Bukkit.getScheduler().runTask(plugin, () -> {
                    // 显示红色粒子效果（血腥钩子）
                    particleLoc.getWorld().spawnParticle(
                        org.bukkit.Particle.DUST,
                        particleLoc,
                        3, // 粒子数量
                        0.1, 0.1, 0.1, // 扩散范围
                        0, // 额外数据
                        new org.bukkit.Particle.DustOptions(org.bukkit.Color.RED, 1.0f) // 红色粒子
                    );

                    // 添加一些血滴效果
                    particleLoc.getWorld().spawnParticle(
                        org.bukkit.Particle.BLOCK,
                        particleLoc,
                        2, // 粒子数量
                        0.05, 0.05, 0.05, // 扩散范围
                        0.1, // 速度
                        org.bukkit.Material.REDSTONE_BLOCK.createBlockData()
                    );
                });

                // 延迟显示，创造轨迹效果
                try {
                    Thread.sleep(20); // 20ms延迟
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        });
    }

    /**
     * 暗影刺客魅影无形技能
     * 自己无法被猎人阵营看到，持续30s，直到攻击敌人后会现形，隐身状态下指南针依然可以追踪
     */
    private boolean handleShadowAssassinInvisibility(Player player) {
        UUID playerId = player.getUniqueId();
        String skillName = "魅影无形";

        // 检查冷却时间
        if (plugin.getProfessionManager().getSkillCooldown().isOnCooldown(playerId, skillName)) {
            int remainingTime = plugin.getProfessionManager().getSkillCooldown().getRemainingCooldown(playerId, skillName);
            ComponentUtil.sendMessage(player, ComponentUtil.error("技能冷却中，剩余时间: " + remainingTime + "秒"));
            return false;
        }

        // 设置隐身状态
        long endTime = System.currentTimeMillis() + 10000; // 10秒
        shadowInvisibilityStates.put(playerId, endTime);

        // 对所有猎人阵营玩家隐藏自己
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(playerId);
        if (gameSession != null) {
            for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
                if (gameSession.getPlayerRole(onlinePlayer.getUniqueId()) == PlayerRole.HUNTER) {
                    onlinePlayer.hidePlayer(plugin, player);
                }
            }
        }

        // 播放效果
        player.playSound(player.getLocation(), Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 0.8f);
        ComponentUtil.sendMessage(player, ComponentUtil.info("魅影无形激活！对猎人阵营隐身10秒"));

        // 冷却时间由ProfessionManager.useActiveSkill()统一处理

        // 10秒后自动现形
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            if (shadowInvisibilityStates.containsKey(playerId)) {
                revealShadowAssassin(player);
            }
        }, 200L); // 10秒 = 200 tick

        return true;
    }

    /**
     * 蜘蛛跳跃技能
     * 蜘蛛向前上方大跳跃，落下时范围内的速通者受到伤害和中毒效果
     */
    private boolean handleSpiderLeap(Player player) {
        UUID playerId = player.getUniqueId();
        String skillName = "跳跃";

        // 检查冷却时间
        if (plugin.getProfessionManager().getSkillCooldown().isOnCooldown(playerId, skillName)) {
            int remainingTime = plugin.getProfessionManager().getSkillCooldown().getRemainingCooldown(playerId, skillName);
            ComponentUtil.sendMessage(player, ComponentUtil.error("技能冷却中，剩余时间: " + remainingTime + "秒"));
            return false;
        }

        // 向前上方跳跃（蜘蛛式跳跃）
        Vector direction = player.getLocation().getDirection().normalize();
        Vector jumpVelocity = direction.multiply(1.5).setY(2.0); // 向前1.5倍速度，向上2.0倍速度
        player.setVelocity(jumpVelocity);

        // 记录蜘蛛跳跃状态，用于落地检测和掉落伤害免疫
        spiderLeapStates.put(playerId, System.currentTimeMillis());

        // 播放起跳效果
        player.playSound(player.getLocation(), Sound.ENTITY_SPIDER_AMBIENT, 1.0f, 1.2f);
        ComponentUtil.sendMessage(player, ComponentUtil.info("蜘蛛跳跃！"));

        // 冷却时间由ProfessionManager.useActiveSkill()统一处理

        return true;
    }

    /**
     * 检查玩家是否处于蜘蛛跳跃状态
     */
    public boolean isSpiderLeaping(UUID playerId) {
        Long leapTime = spiderLeapStates.get(playerId);
        if (leapTime == null) {
            return false;
        }

        // 如果跳跃时间超过10秒，自动清理状态（防止状态残留）
        if (System.currentTimeMillis() - leapTime > 10000) {
            spiderLeapStates.remove(playerId);
            return false;
        }

        return true;
    }

    /**
     * 处理蜘蛛落地，执行落地伤害
     */
    public void handleSpiderLanding(Player spider) {
        UUID playerId = spider.getUniqueId();
        if (!spiderLeapStates.containsKey(playerId)) {
            return; // 不是跳跃状态，忽略
        }

        // 移除跳跃状态
        spiderLeapStates.remove(playerId);

        // 执行落地伤害
        executeSpiderLeapDamage(spider);
    }

    /**
     * 执行蜘蛛跳跃的落地伤害
     */
    public void executeSpiderLeapDamage(Player spider) {
        Location spiderLoc = spider.getLocation();

        // 播放落地效果
        spider.playSound(spiderLoc, Sound.ENTITY_GENERIC_EXPLODE, 1.0f, 1.2f);
        spiderLoc.getWorld().spawnParticle(org.bukkit.Particle.EXPLOSION, spiderLoc, 5, 2, 0.5, 2, 0.1);

        // 检查范围内的速通者
        GameSession spiderGameSession = plugin.getGameManager().getGameSessionByPlayer(spider.getUniqueId());
        if (spiderGameSession != null) {
            com.projectSource.ultimateManhurt.game.PlayerRole spiderRole = spiderGameSession.getPlayerRole(spider.getUniqueId());

            for (Player target : Bukkit.getOnlinePlayers()) {
                com.projectSource.ultimateManhurt.game.PlayerRole targetRole = spiderGameSession.getPlayerRole(target.getUniqueId());

                // 只攻击敌对阵营的玩家（不攻击队友）
                if (targetRole != null && targetRole != spiderRole && targetRole != com.projectSource.ultimateManhurt.game.PlayerRole.SPECTATOR) {
                    double distance = target.getLocation().distance(spiderLoc);

                    if (distance <= 8.0) { // 8格范围内
                        // 计算伤害和中毒等级（距离越近伤害越高）
                        double damageRatio = Math.max(0.25, (8.0 - distance) / 8.0); // 0.25到1.0之间

                        // 伤害：2-12
                        double damage = 2.0 + (10.0 * damageRatio);

                        // 中毒等级：1-5，持续时间5秒
                        int poisonLevel = (int) Math.max(0, Math.min(4, damageRatio * 5)); // 0-4（药水等级从0开始）

                        // 造成伤害
                        target.damage(damage, spider);

                        // 施加中毒效果
                        target.addPotionEffect(new PotionEffect(PotionEffectType.POISON, 100, poisonLevel)); // 5秒 = 100 tick

                        // 播放效果
                        target.playSound(target.getLocation(), Sound.ENTITY_SPIDER_HURT, 1.0f, 1.0f);

                        // 消息反馈
                        ComponentUtil.sendMessage(target, ComponentUtil.warning("被蜘蛛跳跃攻击！受到 " + String.format("%.1f", damage) + " 伤害和中毒效果"));
                        ComponentUtil.sendMessage(spider, ComponentUtil.info("跳跃攻击命中 " + target.getName() + "！"));
                    }
                }
            }
        }
    }

    /**
     * 让暗影刺客现形
     */
    public void revealShadowAssassin(Player player) {
        UUID playerId = player.getUniqueId();

        if (shadowInvisibilityStates.containsKey(playerId)) {
            shadowInvisibilityStates.remove(playerId);

            // 对所有玩家显示自己
            for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
                onlinePlayer.showPlayer(plugin, player);
            }

            // 播放现形效果
            player.playSound(player.getLocation(), Sound.ENTITY_ENDERMAN_SCREAM, 1.0f, 1.0f);
            ComponentUtil.sendMessage(player, ComponentUtil.warning("魅影无形结束，已现形！"));
        }
    }

    /**
     * 检查暗影刺客是否处于隐身状态
     */
    public boolean isShadowAssassinInvisible(UUID playerId) {
        Long endTime = shadowInvisibilityStates.get(playerId);
        if (endTime == null) {
            return false;
        }

        if (System.currentTimeMillis() > endTime) {
            shadowInvisibilityStates.remove(playerId);
            return false;
        }

        return true;
    }

    /**
     * 在指定位置附近寻找玩家
     */
    private Player findNearbyPlayer(Player searcher, Location targetLocation, double radius) {
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(searcher.getUniqueId());
        if (gameSession == null) {
            return null;
        }

        for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
            // 跳过自己
            if (onlinePlayer.equals(searcher)) {
                continue;
            }

            // 只检查游戏中的玩家
            if (gameSession.getPlayerRole(onlinePlayer.getUniqueId()) == null) {
                continue;
            }

            // 检查距离
            if (onlinePlayer.getLocation().distance(targetLocation) <= radius) {
                return onlinePlayer;
            }
        }

        return null;
    }

    /**
     * 猪灵集结技能
     * 将所有队友传送到自己身边，并且获得抗性1加成
     */
    private boolean handlePiglinRally(Player player) {
        UUID playerId = player.getUniqueId();
        String skillName = "猪灵集结";

        // 检查冷却时间
        if (plugin.getProfessionManager().getSkillCooldown().isOnCooldown(playerId, skillName)) {
            int remainingTime = plugin.getProfessionManager().getSkillCooldown().getRemainingCooldown(playerId, skillName);
            ComponentUtil.sendMessage(player, ComponentUtil.error("技能冷却中，剩余时间: " + remainingTime + "秒"));
            return false;
        }

        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(playerId);
        if (gameSession == null) {
            return false;
        }

        Location rallyPoint = player.getLocation();

        // 传送所有队友到自己身边
        for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
            if (gameSession.getPlayerRole(onlinePlayer.getUniqueId()) == PlayerRole.HUNTER &&
                !onlinePlayer.equals(player)) {

                onlinePlayer.teleport(rallyPoint);
                // 给予抗性1加成
                onlinePlayer.addPotionEffect(new PotionEffect(org.bukkit.potion.PotionEffectType.RESISTANCE, 600, 0)); // 30秒抗性1

                ComponentUtil.sendMessage(onlinePlayer, ComponentUtil.info("被猪灵集结传送！获得抗性加成"));
            }
        }

        // 给猪灵自己也加抗性
        player.addPotionEffect(new PotionEffect(org.bukkit.potion.PotionEffectType.RESISTANCE, 600, 0)); // 30秒抗性1

        // 播放效果
        player.playSound(player.getLocation(), Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.2f);
        ComponentUtil.sendMessage(player, ComponentUtil.info("猪灵集结！队友已传送到你身边"));

        // 冷却时间由ProfessionManager.useActiveSkill()统一处理
        return true;
    }

    /**
     * 萨满龙卷风暴技能
     * 将自己周围一定范围变成风场，里面的玩家获得减速4的效果，萨满处于无敌状态
     */
    private boolean handleShamanTornado(Player player) {
        UUID playerId = player.getUniqueId();
        String skillName = "龙卷风暴";

        // 检查冷却时间
        if (plugin.getProfessionManager().getSkillCooldown().isOnCooldown(playerId, skillName)) {
            int remainingTime = plugin.getProfessionManager().getSkillCooldown().getRemainingCooldown(playerId, skillName);
            ComponentUtil.sendMessage(player, ComponentUtil.error("技能冷却中，剩余时间: " + remainingTime + "秒"));
            return false;
        }

        // 萨满获得真正的无敌状态（不可攻击）
        long endTime = System.currentTimeMillis() + 8000; // 8秒
        shamanInvulnerableStates.put(playerId, endTime);

        // 8秒后自动移除无敌状态
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            shamanInvulnerableStates.remove(playerId);
            ComponentUtil.sendMessage(player, ComponentUtil.warning("龙卷风暴结束，无敌状态消失"));
        }, 160L); // 8秒 = 160 tick

        // 播放效果
        player.playSound(player.getLocation(), Sound.ENTITY_ENDER_DRAGON_GROWL, 1.0f, 1.0f);
        ComponentUtil.sendMessage(player, ComponentUtil.info("龙卷风暴！你获得无敌状态"));

        // 启动风场效果
        executeTornadoEffect(player);

        // 冷却时间由ProfessionManager.useActiveSkill()统一处理

        return true;
    }

    /**
     * 执行龙卷风暴的风场效果
     */
    private void executeTornadoEffect(Player shaman) {
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(shaman.getUniqueId());
        if (gameSession == null) {
            return;
        }

        Location center = shaman.getLocation();
        final double[] damageMultiplier = {0.50}; // 初始伤害倍数0.50

        // 持续8秒的风场效果
        for (int i = 0; i < 8; i++) {
            Bukkit.getScheduler().runTaskLater(plugin, () -> {
                // 统计风场内的猎人数量
                int hunterCount = 0;
                for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
                    if (gameSession.getPlayerRole(onlinePlayer.getUniqueId()) == PlayerRole.HUNTER) {
                        double distance = onlinePlayer.getLocation().distance(center);
                        if (distance <= 16.0) { // 16格范围
                            hunterCount++;
                            // 给予减速4效果
                            onlinePlayer.addPotionEffect(new PotionEffect(org.bukkit.potion.PotionEffectType.SLOWNESS, 40, 3)); // 2秒减速4

                            // 禁用跳跃2秒，防止跳跃抵消龙卷风效果
                            plugin.getProfessionManager().getPassiveSkillHandler().disableJump(onlinePlayer.getUniqueId(), 2000);
                        }
                    }
                }

                // 对风场内的猎人造成魔法伤害
                if (hunterCount > 0) {
                    double damage = Math.min(2.50, damageMultiplier[0]) * hunterCount;
                    for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
                        if (gameSession.getPlayerRole(onlinePlayer.getUniqueId()) == PlayerRole.HUNTER) {
                            double distance = onlinePlayer.getLocation().distance(center);
                            if (distance <= 16.0) {
                                onlinePlayer.damage(damage);
                                ComponentUtil.sendMessage(onlinePlayer, ComponentUtil.warning("受到龙卷风暴伤害！"));
                            }
                        }
                    }
                }

                // 增加伤害倍数
                damageMultiplier[0] = Math.min(2.50, damageMultiplier[0] + 0.25);

                // 播放粒子效果
                center.getWorld().spawnParticle(org.bukkit.Particle.CLOUD, center, 20, 4, 2, 4, 0.1);

            }, i * 20L); // 每秒执行一次
        }
    }

    /**
     * 检查萨满是否处于龙卷风暴无敌状态
     */
    public boolean isShamanInvulnerable(UUID playerId) {
        Long endTime = shamanInvulnerableStates.get(playerId);
        if (endTime == null) {
            return false;
        }

        // 检查是否过期
        if (System.currentTimeMillis() > endTime) {
            shamanInvulnerableStates.remove(playerId);
            return false;
        }

        return true;
    }

    /**
     * 记录末影人受到攻击的时间
     */
    public void recordEndermanDamage(UUID playerId) {
        endermanLastDamageTime.put(playerId, System.currentTimeMillis());
    }

    /**
     * 检查末影人是否可以使用传送技能（受攻击3秒内不能使用）
     */
    private boolean canEndermanUseTeleport(UUID playerId) {
        Long lastDamageTime = endermanLastDamageTime.get(playerId);
        if (lastDamageTime == null) {
            return true; // 从未受到攻击，可以使用
        }

        long timeSinceLastDamage = System.currentTimeMillis() - lastDamageTime;
        return timeSinceLastDamage >= 3000; // 3秒后才能使用
    }

    /**
     * 清理过期的状态
     */
    public void cleanupExpiredStates() {
        long currentTime = System.currentTimeMillis();

        // 清理过期的暗影刺客隐身状态
        shadowInvisibilityStates.entrySet().removeIf(entry -> entry.getValue() <= currentTime);

        // 清理过期的蜘蛛跳跃状态
        spiderLeapStates.entrySet().removeIf(entry -> entry.getValue() + 5000 <= currentTime); // 5秒后清理

        // 清理过期的萨满无敌状态
        shamanInvulnerableStates.entrySet().removeIf(entry -> entry.getValue() <= currentTime);

        // 清理过期的末影人受攻击记录
        endermanLastDamageTime.entrySet().removeIf(entry -> entry.getValue() + 10000 <= currentTime); // 10秒后清理
    }

    /**
     * 检查玩家是否在洞穴中
     */
    private boolean isPlayerInCave(Player player) {
        Location playerLoc = player.getLocation();
        int playerY = playerLoc.getBlockY();
        int surfaceY = player.getWorld().getHighestBlockYAt(playerLoc.getBlockX(), playerLoc.getBlockZ());

        // 如果玩家在地表以下10格以上，且上方有实体方块覆盖，认为在洞穴中
        if (playerY < surfaceY - 10) {
            // 检查玩家上方是否有足够的实体方块覆盖（表示在洞穴中）
            int solidBlocksAbove = 0;
            for (int y = playerY + 3; y <= Math.min(playerY + 20, surfaceY); y++) {
                if (player.getWorld().getBlockAt(playerLoc.getBlockX(), y, playerLoc.getBlockZ()).getType().isSolid()) {
                    solidBlocksAbove++;
                }
            }
            // 如果上方有超过5个实体方块，认为在洞穴中
            return solidBlocksAbove > 5;
        }

        return false;
    }

    /**
     * 在洞穴中寻找合适的传送位置
     */
    private Location findCaveTeleportLocation(Location playerLoc, org.bukkit.util.RayTraceResult rayTrace) {
        // 获取击中点的位置
        Vector hitPosition = rayTrace.getHitPosition();
        Location hitLocation = hitPosition.toLocation(playerLoc.getWorld());

        // 从击中点向玩家方向稍微后退，避免传送到方块里
        Vector direction = playerLoc.toVector().subtract(hitPosition).normalize();
        Location teleportLoc = hitLocation.add(direction.multiply(1.5)); // 后退1.5格

        // 确保传送位置是安全的
        Location safeLoc = findSafeCaveLocation(teleportLoc);
        if (safeLoc != null) {
            return safeLoc;
        }

        // 如果找不到安全位置，尝试在击中方块的不同方向寻找
        org.bukkit.block.Block hitBlock = rayTrace.getHitBlock();
        if (hitBlock != null) {
            Location blockLoc = hitBlock.getLocation();

            // 尝试方块的6个面
            Location[] candidates = {
                blockLoc.clone().add(1, 0, 0),   // 东
                blockLoc.clone().add(-1, 0, 0),  // 西
                blockLoc.clone().add(0, 0, 1),   // 南
                blockLoc.clone().add(0, 0, -1),  // 北
                blockLoc.clone().add(0, 1, 0),   // 上
                blockLoc.clone().add(0, -1, 0)   // 下
            };

            for (Location candidate : candidates) {
                Location safe = findSafeCaveLocation(candidate);
                if (safe != null) {
                    return safe;
                }
            }
        }

        // 如果都找不到，返回玩家当前位置附近的安全位置
        return findSafeCaveLocation(playerLoc.clone().add(playerLoc.getDirection().multiply(3)));
    }

    /**
     * 在洞穴中寻找安全的位置
     */
    private Location findSafeCaveLocation(Location loc) {
        if (loc.getWorld() == null) return null;

        // 检查当前位置是否安全
        if (isSafeCaveLocation(loc)) {
            return loc.add(0.5, 0, 0.5); // 居中
        }

        // 在附近寻找安全位置
        for (int y = -2; y <= 2; y++) {
            for (int x = -1; x <= 1; x++) {
                for (int z = -1; z <= 1; z++) {
                    Location testLoc = loc.clone().add(x, y, z);
                    if (isSafeCaveLocation(testLoc)) {
                        return testLoc.add(0.5, 0, 0.5);
                    }
                }
            }
        }

        return null;
    }

    /**
     * 检查洞穴位置是否安全
     */
    private boolean isSafeCaveLocation(Location loc) {
        if (loc.getWorld() == null) return false;

        // 检查脚下有方块，身体和头部位置为空气
        return loc.getBlock().getType().isSolid() && // 脚下有实体方块
               loc.clone().add(0, 1, 0).getBlock().getType().isAir() && // 身体位置为空气
               loc.clone().add(0, 2, 0).getBlock().getType().isAir();   // 头部位置为空气
    }

    /**
     * 恐惧魔王影压技能
     * 15秒冷却一发，保留combo连击系统
     */
    private boolean handleFearLordShadowStrike(Player player) {
        UUID playerId = player.getUniqueId();
        String skillName = "影压";

        // 检查冷却时间
        if (plugin.getProfessionManager().getSkillCooldown().isOnCooldown(playerId, skillName)) {
            int remainingTime = plugin.getProfessionManager().getSkillCooldown().getRemainingCooldown(playerId, skillName);
            ComponentUtil.sendMessage(player, ComponentUtil.error("技能冷却中，剩余时间: " + remainingTime + "秒"));
            return false;
        }

        // 获取连击次数
        int comboCount = fearLordComboCount.getOrDefault(playerId, 0);

        // 计算伤害：5 + 连击次数 * 1.5
        double damage = 5 + (comboCount * 1.5);

        // 执行影压攻击（发射凋灵之首）
        boolean skillExecuted = executeShadowStrike(player, damage);

        if (skillExecuted) {
            ComponentUtil.sendMessage(player, ComponentUtil.info("影压发射！当前连击: " + comboCount + "，伤害: " + damage));

            // 冷却时间由ProfessionManager.useActiveSkill()统一处理
        }

        return true;
    }

    /**
     * 执行影压攻击 - 发射凋灵之首
     */
    private boolean executeShadowStrike(Player player, double damage) {
        Location playerLoc = player.getEyeLocation();
        Vector direction = playerLoc.getDirection().normalize();



        // 发射凋灵之首
        org.bukkit.entity.WitherSkull witherSkull = playerLoc.getWorld().spawn(playerLoc, org.bukkit.entity.WitherSkull.class);

        // 设置凋灵之首属性
        witherSkull.setShooter(player);
        witherSkull.setDirection(direction);
        witherSkull.setVelocity(direction.multiply(2.0)); // 设置飞行速度
        witherSkull.setIsIncendiary(false); // 不点燃方块
        witherSkull.setYield(1.0f); // 不破坏地形

        // 给凋灵之首添加元数据，标记为影压技能
        witherSkull.setMetadata("fear_lord_shadow_strike", new org.bukkit.metadata.FixedMetadataValue(plugin, damage));
        witherSkull.setMetadata("fear_lord_shooter", new org.bukkit.metadata.FixedMetadataValue(plugin, player.getUniqueId().toString()));

        // 播放发射音效
        player.playSound(player.getLocation(), Sound.ENTITY_WITHER_SHOOT, 1.0f, 0.8f);

        // 显示发射粒子效果
        playerLoc.getWorld().spawnParticle(org.bukkit.Particle.SMOKE, playerLoc, 10, 0.5, 0.5, 0.5, 0.1);

        plugin.getLogger().info("恐惧魔王 " + player.getName() + " 发射影压凋灵之首，伤害: " + damage);

        // 返回true表示技能成功发射（命中检查将在ProjectileHitEvent中处理）
        return true;
    }



    /**
     * 莱娜钩爪技能
     * 钩住看到的方块并获得冲力
     */
    private boolean handleLenaGrappleHook(Player player) {
        UUID playerId = player.getUniqueId();
        String skillName = "钩爪";

        // 检查冷却时间
        if (plugin.getProfessionManager().getSkillCooldown().isOnCooldown(playerId, skillName)) {
            int remainingTime = plugin.getProfessionManager().getSkillCooldown().getRemainingCooldown(playerId, skillName);
            ComponentUtil.sendMessage(player, ComponentUtil.error("技能冷却中，剩余时间: " + remainingTime + "秒"));
            return false;
        }

        // 使用射线追踪找到目标方块
        org.bukkit.util.RayTraceResult rayTrace = player.getWorld().rayTraceBlocks(
            player.getEyeLocation(),
            player.getLocation().getDirection(),
            30.0, // 最大30格距离
            org.bukkit.FluidCollisionMode.NEVER,
            true
        );

        if (rayTrace == null || rayTrace.getHitBlock() == null) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("钩爪没有找到目标方块"));
            return false;
        }

        Location targetLoc = rayTrace.getHitBlock().getLocation().add(0.5, 0.5, 0.5);
        Location playerLoc = player.getLocation();

        // 计算钩爪方向和距离
        Vector direction = targetLoc.toVector().subtract(playerLoc.toVector()).normalize();
        double distance = targetLoc.distance(playerLoc);

        // 计算冲力（调整为合理强度）
        double baseForce = Math.min(2.5, distance / 8.0); // 基础冲力降低到2.5，距离系数调整为8

        // 添加向上的冲力分量，确保能够向上移动
        Vector velocity = direction.multiply(baseForce);
        if (velocity.getY() < 0.3) {
            velocity.setY(Math.max(0.5, velocity.getY() + 0.3)); // 向上速度调整为0.5
        }

        // 适度增强整体冲力
        velocity = velocity.multiply(1.25); // 从1.5倍降低到1.2倍

        // 给予玩家冲力
        player.setVelocity(velocity);

        // 设置摔落伤害免疫（5秒）
        lenaFallDamageImmunity.put(playerId, System.currentTimeMillis() + 5000);

        // 显示钩爪轨迹效果
        showGrappleTrajectory(playerLoc, targetLoc);

        // 播放音效
        player.playSound(player.getLocation(), Sound.ENTITY_FISHING_BOBBER_THROW, 1.0f, 1.2f);
        ComponentUtil.sendMessage(player, ComponentUtil.info("钩爪发射！获得摔落伤害免疫"));

        // 冷却时间由ProfessionManager.useActiveSkill()统一处理

        return true;
    }

    /**
     * 显示钩爪轨迹效果
     */
    private void showGrappleTrajectory(Location start, Location end) {
        Vector direction = end.toVector().subtract(start.toVector());
        double distance = direction.length();
        direction.normalize();

        // 异步显示粒子轨迹
        Bukkit.getScheduler().runTaskAsynchronously(plugin, () -> {
            for (double d = 0; d < distance; d += 0.5) {
                Location particleLoc = start.clone().add(direction.clone().multiply(d));

                Bukkit.getScheduler().runTask(plugin, () -> {
                    particleLoc.getWorld().spawnParticle(
                        org.bukkit.Particle.CRIT,
                        particleLoc,
                        2, 0.1, 0.1, 0.1, 0
                    );
                });

                try {
                    Thread.sleep(10);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        });
    }

    /**
     * 检查莱娜是否免疫摔落伤害
     */
    public boolean isLenaFallDamageImmune(UUID playerId) {
        Long immunityEndTime = lenaFallDamageImmunity.get(playerId);
        if (immunityEndTime == null) {
            return false;
        }

        if (System.currentTimeMillis() > immunityEndTime) {
            lenaFallDamageImmunity.remove(playerId);
            return false;
        }

        return true;
    }

    /**
     * 获取恐惧魔王连击次数
     */
    public int getFearLordComboCount(UUID playerId) {
        return fearLordComboCount.getOrDefault(playerId, 0);
    }

    /**
     * 设置恐惧魔王连击次数
     */
    public void setFearLordComboCount(UUID playerId, int count) {
        fearLordComboCount.put(playerId, count);
    }

    /**
     * 清除玩家状态
     */
    public void clearPlayerStates(UUID playerId) {
        lichCurseStates.remove(playerId);
        combatFocusStates.remove(playerId);
        shadowInvisibilityStates.remove(playerId);
        spiderLeapStates.remove(playerId);
        shamanInvulnerableStates.remove(playerId);
        endermanLastDamageTime.remove(playerId);
        fearLordComboCount.remove(playerId);
        lenaFallDamageImmunity.remove(playerId);
        captainSeaCurseStates.remove(playerId);
        zombieLastStandStates.remove(playerId);
        clearFangyuanState(playerId);
        fangyuanLastCreateTime.remove(playerId);
        nightLordDarkMoonStates.remove(playerId);
    }

    /**
     * 清除游戏会话的世界时间记录
     */
    public void clearSessionWorldTimeRecord(String sessionId) {
        worldTimeRecords.remove(sessionId);
    }

    /**
     * 船长海灵诅咒技能
     * 持续10秒，周围60格内的所有友军受到的伤害减免75%，但其中80%的伤害由船长承担
     */
    private boolean handleCaptainSeaCurse(Player player) {
        UUID playerId = player.getUniqueId();
        String skillName = "海灵诅咒";

        // 检查冷却时间
        if (plugin.getProfessionManager().getSkillCooldown().isOnCooldown(playerId, skillName)) {
            int remainingTime = plugin.getProfessionManager().getSkillCooldown().getRemainingCooldown(playerId, skillName);
            ComponentUtil.sendMessage(player, ComponentUtil.error("技能冷却中，剩余时间: " + remainingTime + "秒"));
            return false;
        }

        // 设置海灵诅咒状态
        long endTime = System.currentTimeMillis() + 10000; // 10秒
        captainSeaCurseStates.put(playerId, endTime);

        // 播放效果
        player.playSound(player.getLocation(), Sound.ENTITY_ELDER_GUARDIAN_CURSE, 1.0f, 1.0f);
        player.spawnParticle(Particle.NAUTILUS, player.getLocation().add(0, 1, 0), 30, 2, 2, 2, 0.1);

        ComponentUtil.sendMessage(player, ComponentUtil.info("海灵诅咒激活！为周围友军提供保护"));

        // 10秒后自动移除状态
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            captainSeaCurseStates.remove(playerId);
            ComponentUtil.sendMessage(player, ComponentUtil.warning("海灵诅咒结束"));
        }, 200L); // 10秒 = 200 tick

        // 冷却时间由ProfessionManager.useActiveSkill()统一处理

        return true;
    }

    /**
     * 僵尸回光返照技能
     * 持续8秒，敌人对僵尸造成的伤害不会生效，反而会治疗僵尸
     */
    private boolean handleZombieLastStand(Player player) {
        UUID playerId = player.getUniqueId();
        String skillName = "回光返照";

        // 检查冷却时间
        if (plugin.getProfessionManager().getSkillCooldown().isOnCooldown(playerId, skillName)) {
            int remainingTime = plugin.getProfessionManager().getSkillCooldown().getRemainingCooldown(playerId, skillName);
            ComponentUtil.sendMessage(player, ComponentUtil.error("技能冷却中，剩余时间: " + remainingTime + "秒"));
            return false;
        }

        // 设置回光返照状态
        long endTime = System.currentTimeMillis() + 8000; // 8秒
        zombieLastStandStates.put(playerId, endTime);

        // 播放效果
        player.playSound(player.getLocation(), Sound.ENTITY_ZOMBIE_VILLAGER_CURE, 1.0f, 0.8f);
        player.spawnParticle(Particle.HEART, player.getLocation().add(0, 1, 0), 20, 1, 1, 1, 0.1);

        ComponentUtil.sendMessage(player, ComponentUtil.info("回光返照激活！伤害将转化为治疗"));

        // 8秒后自动移除状态
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            zombieLastStandStates.remove(playerId);
            ComponentUtil.sendMessage(player, ComponentUtil.warning("回光返照结束"));
        }, 160L); // 8秒 = 160 tick

        // 冷却时间由ProfessionManager.useActiveSkill()统一处理

        return true;
    }

    /**
     * 检查船长是否处于海灵诅咒状态
     */
    public boolean isCaptainSeaCurseActive(UUID playerId) {
        Long endTime = captainSeaCurseStates.get(playerId);
        if (endTime == null) {
            return false;
        }

        if (System.currentTimeMillis() > endTime) {
            captainSeaCurseStates.remove(playerId);
            return false;
        }

        return true;
    }

    /**
     * 检查僵尸是否处于回光返照状态
     */
    public boolean isZombieLastStandActive(UUID playerId) {
        Long endTime = zombieLastStandStates.get(playerId);
        if (endTime == null) {
            return false;
        }

        if (System.currentTimeMillis() > endTime) {
            zombieLastStandStates.remove(playerId);
            return false;
        }

        return true;
    }

    /**
     * 机器人陷阱技能
     * 在原地生成隐形地雷
     */
    private boolean handleRobotTrap(Player player) {
        UUID playerId = player.getUniqueId();
        Location trapLocation = player.getLocation().getBlock().getLocation();

        // 检查是否已有地雷在此位置
        if (trapOwners.containsKey(trapLocation)) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("此位置已有地雷！"));
            return false;
        }

        // 检查玩家地雷数量限制（最多9个）
        Set<Location> playerTraps = robotTraps.computeIfAbsent(playerId, k -> new HashSet<>());
        if (playerTraps.size() >= 9) {
            // 移除最旧的地雷（第一个）
            Location oldestTrap = playerTraps.iterator().next();
            playerTraps.remove(oldestTrap);
            trapOwners.remove(oldestTrap);
            trapTriggerTimes.remove(oldestTrap);

            ComponentUtil.sendMessage(player, ComponentUtil.warning("地雷数量已达上限，移除了最旧的地雷"));
        }

        // 放置地雷
        playerTraps.add(trapLocation);
        trapOwners.put(trapLocation, playerId);

        // 播放放置音效
        player.playSound(player.getLocation(), Sound.BLOCK_DISPENSER_DISPENSE, 0.5f, 1.2f);
        ComponentUtil.sendMessage(player, ComponentUtil.info("地雷已放置！当前地雷数量: " + playerTraps.size() + "/9"));

        return true;
    }

    /**
     * 女巫巫毒诅咒技能
     * 8秒内6格敌人每秒失去5%当前生命值
     */
    private boolean handleWitchVoodooCurse(Player player) {
        UUID playerId = player.getUniqueId();
        Location playerLoc = player.getLocation();

        // 获取游戏会话
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(playerId);
        if (gameSession == null) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("无法获取游戏会话"));
            return false;
        }

        PlayerRole witchRole = gameSession.getPlayerRole(playerId);

        // 寻找6格内的敌人
        int affectedCount = 0;
        for (Player target : Bukkit.getOnlinePlayers()) {
            PlayerRole targetRole = gameSession.getPlayerRole(target.getUniqueId());

            // 只影响敌对阵营
            if (targetRole != null && targetRole != witchRole && targetRole != PlayerRole.SPECTATOR) {
                double distance = target.getLocation().distance(playerLoc);
                if (distance <= 6.0) {
                    // 标记为受巫毒诅咒影响
                    voodooAffectedPlayers.put(target.getUniqueId(), System.currentTimeMillis() + 12000); // 改为12秒
                    affectedCount++;

                    ComponentUtil.sendMessage(target, ComponentUtil.warning("受到巫毒诅咒！生命力正在流失..."));
                }
            }
        }

        // 播放音效和粒子效果
        player.playSound(player.getLocation(), Sound.ENTITY_WITCH_AMBIENT, 1.0f, 0.8f);
        player.spawnParticle(Particle.WITCH, player.getLocation().add(0, 1, 0), 30, 2, 2, 2, 0.1);

        if (affectedCount == 0) {
            ComponentUtil.sendMessage(player, ComponentUtil.warning("附近没有敌人，但巫毒诅咒已释放"));
        } else {
            // 启动巫毒诅咒效果
            startVoodooCurseEffect();
            ComponentUtil.sendMessage(player, ComponentUtil.info("巫毒诅咒生效！影响了 " + affectedCount + " 个敌人"));
        }

        return true;
    }

    /**
     * 启动巫毒诅咒持续效果
     */
    private void startVoodooCurseEffect() {
        new BukkitRunnable() {
            int ticks = 0;

            @Override
            public void run() {
                if (ticks >= 240) { // 12秒 = 240 tick
                    this.cancel();
                    return;
                }

                // 每30 tick执行一次伤害
                if (ticks % 30 == 0) {
                    for (Map.Entry<UUID, Long> entry : voodooAffectedPlayers.entrySet()) {
                        UUID playerId = entry.getKey();
                        Long endTime = entry.getValue();

                        if (System.currentTimeMillis() > endTime) {
                            continue; // 诅咒已结束
                        }

                        Player target = Bukkit.getPlayer(playerId);
                        if (target != null && target.isOnline()) {
                            // 造成5%当前生命值的真实伤害
                            double currentHealth = target.getHealth();
                            double damage = currentHealth * 0.05;
                            double newHealth = Math.max(0.5, currentHealth - damage);

                            // 检查是否低于25%最大生命值
                            double maxHealth = target.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).getValue();
                            if (newHealth <= maxHealth * 0.25) {
                                // 立即死亡
                                target.setHealth(0);
                                ComponentUtil.sendMessage(target, ComponentUtil.error("巫毒诅咒夺取了你的生命！"));
                            } else {
                                target.setHealth(newHealth);

                                // 播放受伤音效和粒子效果
                                target.playSound(target.getLocation(), Sound.ENTITY_PLAYER_HURT, 0.5f, 0.8f);
                                target.spawnParticle(Particle.DAMAGE_INDICATOR,
                                    target.getLocation().add(0, 1, 0), 3, 0.3, 0.3, 0.3, 0.1);
                            }
                        }
                    }
                }

                ticks++;
            }
        }.runTaskTimer(plugin, 0L, 1L); // 每1 tick执行，但伤害每30 tick一次
    }

    /**
     * 检查玩家是否受到巫毒诅咒影响
     */
    public boolean isVoodooCursed(UUID playerId) {
        Long endTime = voodooAffectedPlayers.get(playerId);
        if (endTime == null) {
            return false;
        }

        if (System.currentTimeMillis() > endTime) {
            voodooAffectedPlayers.remove(playerId);
            return false;
        }

        return true;
    }

    /**
     * 检查地雷触发
     */
    public void checkTrapTrigger(Player player, Location location) {
        // 检查是否是猎人
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(player.getUniqueId());
        if (gameSession == null) return;

        PlayerRole playerRole = gameSession.getPlayerRole(player.getUniqueId());
        if (playerRole != PlayerRole.HUNTER) return;

        // 检查是否在潜行
        if (player.isSneaking()) return;

        // 检查附近是否有地雷（1.5格范围）
        for (Map.Entry<Location, UUID> entry : trapOwners.entrySet()) {
            Location trapLoc = entry.getKey();
            UUID ownerId = entry.getValue();

            if (trapLoc.getWorld().equals(location.getWorld()) &&
                trapLoc.distance(location) <= 1.5) {

                // 检查触发时间
                Long lastTrigger = trapTriggerTimes.get(trapLoc);
                long currentTime = System.currentTimeMillis();

                if (lastTrigger == null) {
                    // 首次触发，开始1.5秒倒计时
                    trapTriggerTimes.put(trapLoc, currentTime);

                    // 地雷现形
                    trapLoc.getWorld().spawnParticle(Particle.FLAME,
                        trapLoc.clone().add(0.5, 0.1, 0.5), 10, 0.3, 0.1, 0.3, 0.1);

                    // 0.5秒后爆炸
                    Bukkit.getScheduler().runTaskLater(plugin, () -> {
                        if (trapOwners.containsKey(trapLoc)) {
                            triggerTrapExplosion(trapLoc, ownerId, player);
                        }
                    }, 10L); // 0.5秒 = 10 tick
                }
            }
        }
    }

    /**
     * 触发地雷爆炸
     */
    private void triggerTrapExplosion(Location trapLoc, UUID ownerId, Player victim) {
        // 移除地雷
        trapOwners.remove(trapLoc);
        trapTriggerTimes.remove(trapLoc);

        Set<Location> ownerTraps = robotTraps.get(ownerId);
        if (ownerTraps != null) {
            ownerTraps.remove(trapLoc);
        }

        // 检查45秒内重复触发
        Integer comboCount = trapComboCount.getOrDefault(victim.getUniqueId(), 0);
        trapComboCount.put(victim.getUniqueId(), comboCount + 1);

        // 45秒后重置连击
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            trapComboCount.remove(victim.getUniqueId());
        }, 900L); // 45秒 = 900 tick

        // 计算爆炸强度
        float explosionPower = 2.0f + (comboCount * 0.5f);

        // 创建自定义爆炸效果（不破坏方块，不伤害友军）
        createCustomExplosion(trapLoc, explosionPower, ownerId);

        // 通知
        Player owner = Bukkit.getPlayer(ownerId);
        if (owner != null) {
            ComponentUtil.sendMessage(owner, ComponentUtil.info("地雷爆炸！命中 " + victim.getName() +
                (comboCount > 0 ? "（连击 +" + comboCount + "）" : "")));
        }
        ComponentUtil.sendMessage(victim, ComponentUtil.warning("踩到了地雷！"));
    }

    /**
     * 创建自定义地雷效果（只造成伤害，不爆炸破坏方块）
     */
    private void createCustomExplosion(Location explosionLoc, float power, UUID ownerId) {
        // 播放地雷触发音效和粒子效果（不使用爆炸音效）
        explosionLoc.getWorld().playSound(explosionLoc, Sound.ENTITY_FIREWORK_ROCKET_BLAST, 1.0f, 0.8f);
        explosionLoc.getWorld().spawnParticle(Particle.FLAME, explosionLoc, 20, 1, 1, 1, 0.1);
        explosionLoc.getWorld().spawnParticle(Particle.SMOKE, explosionLoc, 10, 1, 1, 1, 0.1);

        // 不再创建原版爆炸，避免破坏方块
        // explosionLoc.getWorld().createExplosion(explosionLoc, power, false, true, null);

        // 获取游戏会话
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(ownerId);
        if (gameSession == null) return;

        PlayerRole ownerRole = gameSession.getPlayerRole(ownerId);
        if (ownerRole == null) return;

        // 计算伤害范围内的玩家
        double damageRadius = power * 2.0; // 伤害半径
        for (Player nearbyPlayer : Bukkit.getOnlinePlayers()) {
            double distance = nearbyPlayer.getLocation().distance(explosionLoc);
            if (distance <= damageRadius) {
                PlayerRole targetRole = gameSession.getPlayerRole(nearbyPlayer.getUniqueId());

                // 只对敌对阵营造成伤害
                if (targetRole != null && targetRole != ownerRole && targetRole != PlayerRole.SPECTATOR) {
                    // 计算基于距离的伤害
                    double damageMultiplier = Math.max(0.2, 1.0 - (distance / damageRadius));
                    double damage = power * 4.0 * damageMultiplier; // 基础伤害保持不变

                    // 造成伤害
                    double currentHealth = nearbyPlayer.getHealth();
                    double newHealth = Math.max(0.5, currentHealth - damage);
                    nearbyPlayer.setHealth(newHealth);

                    // 播放受伤音效
                    nearbyPlayer.playSound(nearbyPlayer.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 1.0f);

                    ComponentUtil.sendMessage(nearbyPlayer, ComponentUtil.warning("受到地雷伤害！伤害: " + String.format("%.1f", damage)));
                }
            }
        }
    }

    /**
     * 方源春秋必成技能
     * 第一次使用：记录所有玩家状态
     * 第二次使用：恢复所有玩家到记录状态
     */
    private boolean handleFangyuanTimeRewind(Player player) {
        UUID playerId = player.getUniqueId();
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(playerId);

        if (gameSession == null) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("无法在非游戏状态下使用此技能"));
            return false;
        }

        String sessionId = gameSession.getSessionId();
        boolean isActive = fangyuanTimeRewindActive.getOrDefault(playerId, false);

        if (!isActive) {
            // 第一次使用：创建时光分身
            // 检查3秒内置冷却
            Long lastCreateTime = fangyuanLastCreateTime.get(playerId);
            if (lastCreateTime != null) {
                long timeSinceLastCreate = System.currentTimeMillis() - lastCreateTime;
                if (timeSinceLastCreate < 3000) { // 3秒内置冷却
                    long remainingTime = (3000 - timeSinceLastCreate) / 1000 + 1;
                    ComponentUtil.sendMessage(player, ComponentUtil.warning("春秋必成内置冷却中，还需等待 " + remainingTime + " 秒"));
                    return false;
                }
            }

            // 如果已存在时光分身，先清理旧的
            if (gameTimeStates.containsKey(sessionId)) {
                gameTimeStates.remove(sessionId);
                ComponentUtil.sendMessage(player, ComponentUtil.warning("旧的时光分身已被摧毁"));
            }

            PlayerTimeState timeState = new PlayerTimeState();

            // 记录所有在线玩家的状态
            for (UUID targetId : gameSession.getPlayerRoles().keySet()) {
                Player targetPlayer = Bukkit.getPlayer(targetId);
                if (targetPlayer != null && targetPlayer.isOnline()) {
                    PlayerSnapshot snapshot = new PlayerSnapshot(targetPlayer);
                    timeState.addPlayerSnapshot(targetId, snapshot);
                }
            }

            gameTimeStates.put(sessionId, timeState);
            fangyuanTimeRewindActive.put(playerId, true);
            fangyuanLastCreateTime.put(playerId, System.currentTimeMillis()); // 记录创建时间

            // 播放效果
            player.playSound(player.getLocation(), Sound.BLOCK_BEACON_ACTIVATE, 1.0f, 1.2f);
            player.spawnParticle(Particle.ENCHANT, player.getLocation().add(0, 1, 0), 30, 1, 1, 1, 0.1);

            ComponentUtil.sendMessage(player, ComponentUtil.info("时光分身已创建！所有玩家状态已记录"));

            // 广播给所有玩家
            for (UUID targetId : gameSession.getPlayerRoles().keySet()) {
                Player targetPlayer = Bukkit.getPlayer(targetId);
                if (targetPlayer != null && targetPlayer.isOnline() && !targetPlayer.equals(player)) {
                    ComponentUtil.sendMessage(targetPlayer, ComponentUtil.warning("方源创建了时光分身！"));
                }
            }

            return false; // 不进入冷却，等待第二次使用
        } else {
            // 第二次使用：回到时光分身状态
            PlayerTimeState timeState = gameTimeStates.get(sessionId);
            if (timeState == null) {
                ComponentUtil.sendMessage(player, ComponentUtil.error("时光分身数据丢失！"));
                fangyuanTimeRewindActive.put(playerId, false);
                return false;
            }

            // 恢复所有玩家状态
            int restoredCount = 0;
            for (UUID targetId : timeState.getPlayerIds()) {
                Player targetPlayer = Bukkit.getPlayer(targetId);
                if (targetPlayer != null && targetPlayer.isOnline()) {
                    PlayerSnapshot snapshot = timeState.getPlayerSnapshot(targetId);
                    if (snapshot != null) {
                        snapshot.restoreToPlayer(targetPlayer);
                        restoredCount++;

                        // 播放恢复效果
                        targetPlayer.playSound(targetPlayer.getLocation(), Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 0.8f);
                        targetPlayer.spawnParticle(Particle.PORTAL, targetPlayer.getLocation().add(0, 1, 0), 20, 0.5, 0.5, 0.5, 0.1);
                    }
                }
            }

            // 重置春秋必成状态，但保留时光分身数据供春秋蝉使用
            fangyuanTimeRewindActive.put(playerId, false);

            ComponentUtil.sendMessage(player, ComponentUtil.info("春秋必成！已恢复 " + restoredCount + " 名玩家到时光分身状态"));

            // 广播给所有玩家
            for (UUID targetId : gameSession.getPlayerRoles().keySet()) {
                Player targetPlayer = Bukkit.getPlayer(targetId);
                if (targetPlayer != null && targetPlayer.isOnline() && !targetPlayer.equals(player)) {
                    ComponentUtil.sendMessage(targetPlayer, ComponentUtil.warning("方源使用春秋必成！所有人回到了时光分身状态"));
                }
            }

            return true; // 进入冷却
        }
    }

    /**
     * 暗夜领主暗月升起技能
     * 30秒内世界变为黑夜，暗夜领主可以飞行
     * 支持多个暗夜领主同时使用，记录原始时间并正确恢复
     */
    private boolean handleNightLordDarkMoon(Player player) {
        UUID playerId = player.getUniqueId();
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(playerId);

        if (gameSession == null) {
            ComponentUtil.sendMessage(player, ComponentUtil.error("无法在非游戏状态下使用此技能"));
            return false;
        }

        String sessionId = gameSession.getSessionId();

        // 设置技能持续时间（30秒）
        long endTime = System.currentTimeMillis() + 30000;
        nightLordDarkMoonStates.put(playerId, endTime);

        // 处理世界时间记录
        WorldTimeRecord timeRecord = worldTimeRecords.get(sessionId);
        if (timeRecord == null) {
            // 第一个暗夜领主使用技能，记录原始时间
            long originalTime = gameSession.getGameWorld().getOverworld().getTime();
            timeRecord = new WorldTimeRecord(originalTime);
            worldTimeRecords.put(sessionId, timeRecord);

            // 设置世界时间为夜晚
            gameSession.getGameWorld().setTime(18000); // 夜晚时间


        } else {
            // 已有其他暗夜领主在使用技能，增加计数
            timeRecord.incrementActiveNightLords();


        }

        // 给暗夜领主飞行能力
        player.setAllowFlight(true);
        player.setFlying(true);

        // 播放效果
        player.playSound(player.getLocation(), Sound.ENTITY_WITHER_SPAWN, 1.0f, 0.6f);
        player.spawnParticle(Particle.SMOKE, player.getLocation().add(0, 2, 0), 50, 2, 2, 2, 0.1);

        ComponentUtil.sendMessage(player, ComponentUtil.info("暗月升起！黑夜降临，获得飞行能力30秒"));

        // 广播给所有玩家
        for (UUID targetId : gameSession.getPlayerRoles().keySet()) {
            Player targetPlayer = Bukkit.getPlayer(targetId);
            if (targetPlayer != null && targetPlayer.isOnline() && !targetPlayer.equals(player)) {
                ComponentUtil.sendMessage(targetPlayer, ComponentUtil.warning("暗夜领主使用暗月升起！黑夜降临了"));
                targetPlayer.playSound(targetPlayer.getLocation(), Sound.ENTITY_WITHER_AMBIENT, 0.5f, 0.8f);
            }
        }

        // 30秒后移除效果
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            handleNightLordDarkMoonEnd(playerId, sessionId, gameSession);
        }, 600L); // 30秒 = 600 tick

        return true;
    }

    /**
     * 处理暗夜领主暗月升起技能结束
     */
    private void handleNightLordDarkMoonEnd(UUID playerId, String sessionId, GameSession gameSession) {
        // 移除玩家状态
        nightLordDarkMoonStates.remove(playerId);

        Player currentPlayer = Bukkit.getPlayer(playerId);
        if (currentPlayer != null && currentPlayer.isOnline()) {
            // 移除飞行能力
            currentPlayer.setAllowFlight(false);
            currentPlayer.setFlying(false);

            ComponentUtil.sendMessage(currentPlayer, ComponentUtil.warning("暗月升起效果结束"));
        }

        // 处理世界时间恢复
        WorldTimeRecord timeRecord = worldTimeRecords.get(sessionId);
        if (timeRecord != null) {
            timeRecord.decrementActiveNightLords();



            // 如果没有其他暗夜领主在使用技能，恢复原始时间
            if (!timeRecord.hasActiveNightLords()) {
                worldTimeRecords.remove(sessionId);

                if (gameSession.getState().isActive()) {
                    long originalTime = timeRecord.getOriginalTime();
                    gameSession.getGameWorld().setTime(originalTime);



                    // 通知所有玩家
                    for (UUID targetId : gameSession.getPlayerRoles().keySet()) {
                        Player targetPlayer = Bukkit.getPlayer(targetId);
                        if (targetPlayer != null && targetPlayer.isOnline()) {
                            ComponentUtil.sendMessage(targetPlayer, ComponentUtil.info("暗月升起效果全部结束，时间恢复"));
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取游戏会话的时光状态（供被动技能使用）
     */
    public PlayerTimeState getGameTimeState(String sessionId) {
        return gameTimeStates.get(sessionId);
    }

    /**
     * 检查是否有时光分身记录
     */
    public boolean hasTimeState(String sessionId) {
        return gameTimeStates.containsKey(sessionId);
    }

    /**
     * 清理游戏会话的时光分身数据
     */
    public void clearGameTimeState(String sessionId) {
        gameTimeStates.remove(sessionId);
    }

    /**
     * 清理玩家的方源状态
     */
    public void clearFangyuanState(UUID playerId) {
        fangyuanTimeRewindActive.remove(playerId);
    }
}
